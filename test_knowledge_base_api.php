<?php

require_once 'vendor/autoload.php';

use GuzzleHttp\Client;

$client = new Client([
    'base_uri' => 'http://127.0.0.1:8000/',
    'timeout' => 30,
]);

try {
    // First, get auth token
    $loginResponse = $client->post('api/v1/auth/login', [
        'json' => [
            'email' => '<EMAIL>',
            'password' => 'password'
        ]
    ]);

    $loginData = json_decode($loginResponse->getBody(), true);
    $token = $loginData['data']['access_token'];

    echo "✅ Login successful\n";

    // Test knowledge bases API
    $response = $client->get('api/v1/auth/knowledge-bases', [
        'headers' => [
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json',
        ]
    ]);

    $data = json_decode($response->getBody(), true);
    
    echo "📋 Knowledge Bases API Response:\n";
    echo "Status Code: " . $response->getStatusCode() . "\n";
    
    if (isset($data['data']) && !empty($data['data'])) {
        $firstKnowledgeBase = $data['data'][0];
        echo "\n🔍 First Knowledge Base Fields:\n";
        foreach ($firstKnowledgeBase as $key => $value) {
            if (is_array($value)) {
                echo "- $key: " . json_encode($value) . "\n";
            } else {
                echo "- $key: $value\n";
            }
        }
        
        echo "\n🔒 Security Check:\n";
        echo "- Has 'id' field: " . (isset($firstKnowledgeBase['id']) ? "❌ YES (should be hidden)" : "✅ NO (correctly hidden)") . "\n";
        echo "- Has 'owner_id' field: " . (isset($firstKnowledgeBase['owner_id']) ? "❌ YES (should be hidden)" : "✅ NO (correctly hidden)") . "\n";
        echo "- Has 'owner_type' field: " . (isset($firstKnowledgeBase['owner_type']) ? "❌ YES (should be hidden)" : "✅ NO (correctly hidden)") . "\n";
        echo "- Has 'uuid' field: " . (isset($firstKnowledgeBase['uuid']) ? "✅ YES (should be visible)" : "❌ NO (should be visible)") . "\n";
    } else {
        echo "No knowledge bases found in response\n";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    if (method_exists($e, 'hasResponse') && $e->hasResponse()) {
        echo "Response: " . $e->getResponse()->getBody() . "\n";
    }
}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\ChatBot\Models\Query;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Models\KnowledgeBase;
use Modules\ChatBot\Jobs\QueryProcessingJob;
use Modules\User\Models\User;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;

class TestQueryEmbedding extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:query-embedding 
                            {--question= : Custom question to test (default: "What is Laravel?")}
                            {--bot-id= : Specific bot ID to use}
                            {--user-id= : Specific user ID to use}
                            {--with-files : Include knowledge base files in query}
                            {--top-k=5 : Number of top results to retrieve}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test query embedding workflow with Redis queue for Python RAG service';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Testing Query Embedding Workflow...');
        $this->newLine();

        // Get parameters
        $question = $this->option('question') ?: 'What is Laravel and how does it work?';
        $topK = (int) $this->option('top-k');
        $withFiles = $this->option('with-files');

        // Get or create test user
        $user = $this->getTestUser();
        if (!$user) {
            return 1;
        }

        // Get or create test bot
        $bot = $this->getTestBot();
        if (!$bot) {
            return 1;
        }

        // Create test conversation
        $conversation = $this->createTestConversation($bot, $user);
        
        // Create test messages
        $userMessage = $this->createUserMessage($conversation, $question);
        $assistantMessage = $this->createAssistantMessage($conversation);

        // Get file IDs if requested
        $fileIds = $withFiles ? $this->getKnowledgeBaseIds($bot) : null;

        // Create query for embedding
        $query = $this->createQuery($user, $bot, $conversation, $userMessage, $assistantMessage, $question, $fileIds, $topK);

        // Test Redis queue dispatch
        return $this->testQueryDispatch($query);
    }

    /**
     * Get or use specified test user.
     */
    private function getTestUser(): ?User
    {
        $userId = $this->option('user-id');
        
        if ($userId) {
            $user = User::find($userId);
            if (!$user) {
                $this->error("User with ID {$userId} not found.");
                return null;
            }
        } else {
            $user = User::first();
            if (!$user) {
                $this->error('No users found in database. Please create a user first.');
                return null;
            }
        }

        $this->info("✅ Using User: {$user->full_name} (ID: {$user->id})");
        return $user;
    }

    /**
     * Get or use specified test bot.
     */
    private function getTestBot(): ?Bot
    {
        $botId = $this->option('bot-id');
        
        if ($botId) {
            $bot = Bot::find($botId);
            if (!$bot) {
                $this->error("Bot with ID {$botId} not found.");
                return null;
            }
        } else {
            $bot = Bot::first();
            if (!$bot) {
                $this->error('No bots found in database. Please create a bot first.');
                return null;
            }
        }

        $this->info("✅ Using Bot: {$bot->name} (ID: {$bot->id})");
        return $bot;
    }

    /**
     * Create test conversation.
     */
    private function createTestConversation(Bot $bot, User $user): Conversation
    {
        $conversation = Conversation::create([
            'uuid' => Str::uuid(),
            'bot_id' => $bot->id,
            'user_id' => $user->id,
            'title' => 'Test Query Embedding - ' . now()->format('Y-m-d H:i:s'),
            'status' => 'active',
            'metadata' => ['test' => true, 'created_by_command' => true]
        ]);

        $this->info("✅ Created Conversation: {$conversation->title} (ID: {$conversation->id})");
        return $conversation;
    }

    /**
     * Create test user message.
     */
    private function createUserMessage(Conversation $conversation, string $question): Message
    {
        $userMessage = Message::create([
            'uuid' => Str::uuid(),
            'conversation_id' => $conversation->id,
            'role' => 'user',
            'content' => $question,
            'status' => 'completed',
            'metadata' => ['test' => true, 'created_by_command' => true]
        ]);

        $this->info("✅ Created User Message: \"{$question}\" (ID: {$userMessage->id})");
        return $userMessage;
    }

    /**
     * Create test assistant message.
     */
    private function createAssistantMessage(Conversation $conversation): Message
    {
        $assistantMessage = Message::create([
            'uuid' => Str::uuid(),
            'conversation_id' => $conversation->id,
            'role' => 'assistant',
            'content' => '',
            'status' => 'pending',
            'metadata' => ['test' => true, 'created_by_command' => true]
        ]);

        $this->info("✅ Created Assistant Message (ID: {$assistantMessage->id})");
        return $assistantMessage;
    }

    /**
     * Get knowledge base IDs for the bot.
     */
    private function getKnowledgeBaseIds(Bot $bot): ?array
    {
        $kbIds = $bot->knowledgeBases()->where('status', 'ready')->pluck('id')->toArray();
        
        if (empty($kbIds)) {
            $this->warn('⚠️  No ready knowledge bases found for this bot');
            return null;
        }

        $this->info("✅ Found " . count($kbIds) . " knowledge base(s): " . implode(', ', $kbIds));
        return $kbIds;
    }

    /**
     * Create query for embedding.
     */
    private function createQuery(User $user, Bot $bot, Conversation $conversation, Message $userMessage, Message $assistantMessage, string $question, ?array $fileIds, int $topK): Query
    {
        $query = Query::create([
            'uuid' => Str::uuid(),
            'question' => $question,
            'owner_id' => $user->id,
            'owner_type' => get_class($user),
            'bot_id' => $bot->id,
            'conversation_id' => $conversation->id,
            'message_id' => $userMessage->id,
            'file_ids' => $fileIds,
            'top_k' => $topK,
            'collection' => 'documents',
            'status' => 'pending',
            'metadata' => [
                'test' => true,
                'created_by_command' => true,
                'assistant_message_id' => $assistantMessage->id
            ]
        ]);

        $this->info("✅ Created Query: {$query->uuid} (ID: {$query->id})");
        $this->info("   - Top K: {$topK}");
        $this->info("   - File IDs: " . ($fileIds ? json_encode($fileIds) : 'null'));
        
        return $query;
    }

    /**
     * Test query dispatch to Redis queue.
     */
    private function testQueryDispatch(Query $query): int
    {
        $this->newLine();
        $this->info('🚀 Dispatching Query to Redis Queue...');

        // Check Redis queue before
        $queueName = 'rag-processing';
        $beforeCount = Redis::llen($queueName);
        $this->info("📊 Queue length before: {$beforeCount}");

        // Dispatch query processing job
        QueryProcessingJob::dispatch($query);
        $this->info('✅ Query processing job dispatched');

        // Wait a moment
        sleep(2);

        // Check Redis queue after
        $afterCount = Redis::llen($queueName);
        $this->info("📊 Queue length after: {$afterCount}");

        // Check queue content
        $queueContent = Redis::lrange($queueName, 0, -1);
        $this->info("📊 Queue content count: " . count($queueContent));

        if (count($queueContent) > 0) {
            $this->newLine();
            $this->info('🎉 SUCCESS: Query job found in Redis queue for Python!');
            
            $payload = json_decode($queueContent[0], true);

            if ($payload) {
                $this->table(['Field', 'Value'], [
                    ['Task ID', $payload['taskId'] ?? 'N/A'],
                    ['Type', $payload['type'] ?? 'N/A'],
                    ['Query ID', $payload['queryId'] ?? 'N/A'],
                    ['Question', $payload['question'] ?? 'N/A'],
                    ['Owner ID', $payload['ownerId'] ?? 'N/A'],
                    ['Collection', $payload['collection'] ?? 'N/A'],
                    ['Top K', $payload['topK'] ?? 'N/A'],
                    ['Webhook URL', $payload['webhookUrl'] ?? 'N/A'],
                    ['File IDs', isset($payload['fileIds']) ? json_encode($payload['fileIds']) : 'null'],
                ]);

                $this->newLine();
                $this->info('📋 Full payload for Python service:');
                $this->line(json_encode($payload, JSON_PRETTY_PRINT));
            }
        } else {
            $this->warn('⚠️  No jobs found in Redis queue');
        }

        // Refresh query to check status
        $query->refresh();
        $this->newLine();
        $this->info("📊 Query status after dispatch: {$query->status}");

        return 0;
    }
}

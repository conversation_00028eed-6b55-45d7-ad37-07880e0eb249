<?php

namespace Modules\ActiveCode\Facades;

use Illuminate\Support\Facades\Facade;
use Modules\ActiveCode\Models\ActiveCode;
use Modules\ActiveCode\Services\ActiveCodeService;

/**
 * ActiveCodeFacade
 *
 * Facade for ActiveCodeService providing convenient static access to active code functionality.
 * All user-facing messages are localized using <PERSON><PERSON>'s JSON-based translation system.
 *
 * @method static ActiveCode generate(string $identifier, string $type = 'verification', int|null $expiryMinutes = null)
 * @method static array verify(string $code, string $identifier, string $type = 'verification')
 * @method static array resend(string $identifier, string $type = 'verification')
 * @method static void cleanup()
 * @method static void clearSettingsCache()
 * @method static void warmSettingsCache()
 *
 * @see ActiveCodeService
 */
class ActiveCodeFacade extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor(): string
    {
        return 'activecode.service';
    }
}

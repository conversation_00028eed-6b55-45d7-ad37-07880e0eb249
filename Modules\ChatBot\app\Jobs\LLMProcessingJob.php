<?php

namespace Modules\ChatBot\Jobs;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Modules\ChatBot\Models\Query;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Events\QueryResultsReceived;
use Modules\ChatBot\Facades\AIFacade;
use Modules\ChatBot\Enums\MessageStatus;
use Exception;

class LLMProcessingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected Query $query;
    protected array $ragResults;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 300;

    /**
     * Create a new job instance.
     */
    public function __construct(Query $query, array $ragResults = [])
    {
        $this->query = $query;
        $this->ragResults = $ragResults;
        $this->onQueue(config('chatbot.llm.queue_name', 'llm-processing'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('LLMProcessingJob started', [
                'query_id' => $this->query->id,
                'query_uuid' => $this->query->uuid,
                'question' => $this->query->question,
                'rag_results_count' => count($this->ragResults),
            ]);

            // Update query status to LLM processing
            $this->query->update([
                'status' => 'llm_processing',
                'metadata' => array_merge($this->query->metadata ?? [], [
                    'llm_processing_started_at' => now()->toISOString(),
                    'rag_results_count' => count($this->ragResults),
                ])
            ]);

            // Get conversation and bot
            $conversation = Conversation::find($this->query->conversation_id);
            $bot = $conversation->bot;

            if (!$conversation || !$bot) {
                throw new Exception('Conversation or bot not found');
            }

            // Get user message (question)
            $userMessage = Message::find($this->query->message_id);

            if (!$userMessage) {
                throw new Exception('User message not found');
            }

            // Get existing assistant message (created by MessageService)
            $assistantMessageId = $this->query->metadata['assistant_message_id'] ?? null;
            if (!$assistantMessageId) {
                throw new Exception('Assistant message ID not found in query metadata');
            }

            $assistantMessage = Message::find($assistantMessageId);
            if (!$assistantMessage) {
                throw new Exception("Assistant message not found: {$assistantMessageId}");
            }

            // Update assistant message with RAG context
            $this->updateAssistantMessageWithRAGContext($assistantMessage);

            // Get conversation context messages
            $messages = $this->getConversationContext($conversation);

            // Temporarily modify bot's system prompt to include RAG context
            $originalSystemPrompt = $bot->system_prompt;
            $ragContext = $this->buildContextFromRAG();
            $enhancedSystemPrompt = $this->buildEnhancedSystemPrompt($originalSystemPrompt, $ragContext);

            // Temporarily update bot's system prompt
            $bot->system_prompt = $enhancedSystemPrompt;

            // Use AIService to generate response (like ProcessChatMessage does)
            $success = AIFacade::getAssistantPrompts($bot, $userMessage, $assistantMessage, $messages);

            // Restore original system prompt
            $bot->system_prompt = $originalSystemPrompt;

            if (!$success) {
                throw new Exception('AI service failed to generate response');
            }

            // Mark query as completed with final results
            $finalResults = [
                'rag_results' => $this->ragResults,
                'assistant_message_id' => $assistantMessage->id,
                'context_used' => $this->buildContextFromRAG(),
                'processing_time' => now()->diffInSeconds($this->query->processing_started_at),
            ];

            $this->query->markAsCompleted($finalResults, [
                'llm_processing_completed_at' => now()->toISOString(),
                'total_processing_time' => now()->diffInSeconds($this->query->processing_started_at),
                'workflow_type' => 'rag_llm_enhanced',
                'assistant_message_id' => $assistantMessage->id,
            ]);

            // Broadcast final results to user
            $this->broadcastFinalResults();

            Log::info('LLMProcessingJob completed successfully', [
                'query_id' => $this->query->id,
                'query_uuid' => $this->query->uuid,
                'assistant_message_id' => $assistantMessage->id,
                'response_length' => strlen($assistantMessage->content ?? ''),
            ]);

        } catch (Exception $e) {
            Log::error('LLMProcessingJob failed', [
                'query_id' => $this->query->id,
                'query_uuid' => $this->query->uuid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Mark query as failed
            $this->query->markAsFailed($e->getMessage(), [
                'llm_job_failed_at' => now()->toISOString(),
                'attempt' => $this->attempts(),
                'rag_results_available' => !empty($this->ragResults),
            ]);

            throw $e;
        }
    }

    /**
     * Update assistant message metadata with RAG context.
     */
    private function updateAssistantMessageWithRAGContext(Message $assistantMessage): void
    {
        // Build RAG context for metadata
        $ragContext = $this->buildContextFromRAG();

        // Update assistant message metadata
        $assistantMessage->update([
            'metadata' => array_merge($assistantMessage->metadata ?? [], [
                'query_id' => $this->query->uuid,
                'rag_enhanced' => true,
                'rag_sources_count' => count($this->ragResults),
                'rag_context' => $ragContext,
                'llm_processing_started_at' => now()->toISOString(),
            ])
        ]);
    }

    /**
     * Get conversation context messages (like ProcessChatMessage does).
     */
    private function getConversationContext(Conversation $conversation): Collection
    {
        return Message::where('conversation_id', $conversation->id)
            ->whereIn('role', ['user', 'assistant'])
            ->whereIn('status', [MessageStatus::COMPLETED, MessageStatus::PENDING])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->reverse();
    }

    /**
     * Build context from RAG results.
     */
    private function buildContextFromRAG(): string
    {
        if (empty($this->ragResults)) {
            return '';
        }

        $contextParts = [];
        foreach ($this->ragResults as $result) {
            if (isset($result['snippet']) && !empty($result['snippet'])) {
                $score = isset($result['score']) ? number_format($result['score'] * 100, 1) : 'N/A';
                $contextParts[] = "Relevance: {$score}% - " . $result['snippet'];
            }
        }

        return implode("\n\n", $contextParts);
    }

    /**
     * Build enhanced system prompt with RAG context.
     */
    private function buildEnhancedSystemPrompt(string $originalPrompt, string $ragContext): string
    {
        if (empty($ragContext)) {
            return $originalPrompt;
        }

        $enhancedPrompt = $originalPrompt . "\n\n";
        $enhancedPrompt .= "=== KNOWLEDGE BASE CONTEXT ===\n";
        $enhancedPrompt .= "Use the following relevant information from the knowledge base to enhance your response:\n\n";
        $enhancedPrompt .= $ragContext . "\n\n";
        $enhancedPrompt .= "=== INSTRUCTIONS ===\n";
        $enhancedPrompt .= "- Prioritize information from the knowledge base context above\n";
        $enhancedPrompt .= "- If the knowledge base contains relevant information, use it in your response\n";
        $enhancedPrompt .= "- If the knowledge base doesn't contain relevant information, respond based on your general knowledge\n";
        $enhancedPrompt .= "- Always maintain a helpful and professional tone\n";
        $enhancedPrompt .= "- Cite specific information when using knowledge base content\n";

        return $enhancedPrompt;
    }

    /**
     * Broadcast final results to user.
     */
    private function broadcastFinalResults(): void
    {
        $broadcastData = [
            'query_id' => $this->query->uuid,
            'question' => $this->query->question,
            'results' => $this->query->getFormattedResults(),
            'timestamp' => now()->toISOString(),
            'bot_id' => $this->query->bot_id,
            'conversation_id' => $this->query->conversation_id,
            'message_id' => $this->query->message_id,
            'status' => $this->query->status,
            'metadata' => $this->query->metadata,
            'llm_enhanced' => true,
        ];

        // Fire the event which will handle broadcasting
        event(new QueryResultsReceived($this->query, $broadcastData));

        $query = Query::find($this->queryId);
        if ($query) {
            Log::info('Final query results broadcasted', [
                'query_id' => $query->uuid,
                'conversation_id' => $query->conversation_id,
                'owner_id' => $query->owner_id,
            ]);
        }
    }

    /**
     * Handle job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error('LLMProcessingJob permanently failed', [
            'query_id' => $this->query->id,
            'query_uuid' => $this->query->uuid,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);

        // Mark query as permanently failed
        $this->query->markAsFailed($exception->getMessage(), [
            'llm_permanently_failed_at' => now()->toISOString(),
            'total_attempts' => $this->attempts(),
            'rag_results_available' => !empty($this->ragResults),
        ]);
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'llm',
            'query:' . $this->query->uuid,
            'owner:' . $this->query->owner_id,
            'bot:' . ($this->query->bot_id ?? 'none'),
        ];
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [30, 60, 120]; // 30s, 1min, 2min
    }
}

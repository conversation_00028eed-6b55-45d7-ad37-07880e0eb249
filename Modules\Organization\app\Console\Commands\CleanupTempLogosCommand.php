<?php

namespace Modules\Organization\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

/**
 * CleanupTempLogosCommand
 * 
 * Command to clean up old temporary logo files from organization-logos/temp directory.
 */
class CleanupTempLogosCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'organization:cleanup-temp-logos {--hours=24 : Hours to keep temp files}';

    /**
     * The console command description.
     */
    protected $description = 'Clean up old temporary organization logo files';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $hours = (int) $this->option('hours');
        
        $this->info("Starting cleanup of temporary logo files older than {$hours} hours...");

        $tempDirectory = 'organization-logos/temp';
        $disk = Storage::disk('public');
        
        if (!$disk->exists($tempDirectory)) {
            $this->info('Temp directory does not exist. Nothing to clean up.');
            return self::SUCCESS;
        }

        $files = $disk->files($tempDirectory);
        $deletedCount = 0;
        $cutoffTime = Carbon::now()->subHours($hours);

        foreach ($files as $file) {
            $lastModified = Carbon::createFromTimestamp($disk->lastModified($file));
            
            if ($lastModified->lt($cutoffTime)) {
                $disk->delete($file);
                $deletedCount++;
                $this->line("Deleted: {$file} (modified {$lastModified->diffForHumans()})");
            }
        }

        $this->info("Cleaned up {$deletedCount} temporary logo files.");

        return self::SUCCESS;
    }
}

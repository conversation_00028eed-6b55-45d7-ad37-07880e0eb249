<?php

namespace Modules\Language\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Role\Models\Permission;
use Modules\Role\Models\Role;

class LanguagePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedLanguagePermissions();
        $this->assignPermissionsToSuperAdmin();
    }

    /**
     * Seed language module permissions.
     */
    private function seedLanguagePermissions(): void
    {
        $permissions = [
            [
                'name' => 'language.view',
                'display_name' => 'View Languages',
                'description' => 'Permission to view languages list and details',
                'module_name' => 'language',
                'sort_order' => 1,
                'guard_name' => 'api',
            ],
            [
                'name' => 'language.create',
                'display_name' => 'Create Languages',
                'description' => 'Permission to create new languages',
                'module_name' => 'language',
                'sort_order' => 2,
                'guard_name' => 'api',
            ],
            [
                'name' => 'language.edit',
                'display_name' => 'Edit Languages',
                'description' => 'Permission to update existing languages and set default language',
                'module_name' => 'language',
                'sort_order' => 3,
                'guard_name' => 'api',
            ],
            [
                'name' => 'language.delete',
                'display_name' => 'Delete Languages',
                'description' => 'Permission to soft delete and restore languages',
                'module_name' => 'language',
                'sort_order' => 4,
                'guard_name' => 'api',
            ],
            [
                'name' => 'language.destroy',
                'display_name' => 'Destroy Languages',
                'description' => 'Permission to permanently delete languages',
                'module_name' => 'language',
                'sort_order' => 5,
                'guard_name' => 'api',
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => 'api'],
                array_merge($permission, ['guard_name' => 'api'])
            );
        }
    }

    /**
     * Assign language permissions to super-admin role.
     */
    private function assignPermissionsToSuperAdmin(): void
    {
        $superAdmin = Role::where('name', 'super-admin')->where('guard_name', 'api')->first();

        if ($superAdmin) {
            $languagePermissions = Permission::where('module_name', 'language')->where('guard_name', 'api')->get();
            
            // Gán tất cả quyền language cho super-admin
            foreach ($languagePermissions as $permission) {
                if (!$superAdmin->hasPermissionTo($permission->name)) {
                    $superAdmin->givePermissionTo($permission->name);
                }
            }
        }
    }
}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\Organization\Models\Organization;
use Modules\User\Models\User;
use Modules\Organization\Http\Controllers\Auth\OrganizationController;
use Modules\Organization\Http\Requests\Organization\ListOrganizationRequest;
use Illuminate\Http\Request;

class TestOrganizationList extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:organization-list {user_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test organization list for a user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id') ?? 1;
        
        $user = User::find($userId);
        
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return;
        }

        $this->info("Testing organization list for user: {$user->email}");
        
        try {
            // Test authorization first
            $this->info('Testing authorization...');
            $canViewAny = $user->can('viewAny', Organization::class);
            $this->info("Can viewAny: " . ($canViewAny ? 'YES' : 'NO'));
            
            // Test direct query
            $this->info('Testing direct query...');
            $organizations = Organization::all();
            $this->info("Total organizations in database: " . $organizations->count());
            
            foreach ($organizations as $org) {
                $this->line("- ID: {$org->id}, Name: {$org->name}, Type: {$org->type}, Owner: {$org->owner_id}");
            }
            
            // Test with filter
            $this->info('Testing with filter...');
            $request = new Request();
            $filter = new \Modules\Organization\Http\Filters\OrganizationFilter($request);
            $filteredOrgs = Organization::query()->filter($filter)->get();
            $this->info("Filtered organizations count: " . $filteredOrgs->count());
            
        } catch (\Exception $e) {
            $this->error("✗ Failed to get organization list:");
            $this->error($e->getMessage());
            $this->error("File: {$e->getFile()}:{$e->getLine()}");
            
            if ($this->option('verbose')) {
                $this->error("Stack trace:");
                $this->error($e->getTraceAsString());
            }
        }
    }
}

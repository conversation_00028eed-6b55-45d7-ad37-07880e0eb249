<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON><PERSON>\Auth\Http\Controllers\AuthController;
use Modules\Auth\Http\Controllers\OAuthController;
use Modules\Auth\Http\Controllers\ProfileController;

// Public authentication routes with rate limiting
Route::prefix('v1/auth')->name('auth.')->group(function () {
    // Registration with rate limiting
    Route::post('register', [AuthController::class, 'register'])
        ->middleware('auth.rate.limit:register')
        ->name('register');

    // Email verification with rate limiting
    Route::post('verify-email', [AuthController::class, 'verifyEmail'])
        ->middleware('auth.rate.limit:email_verification')
        ->name('verify-email');

    Route::post('resend-verification', [AuthController::class, 'resendVerification'])
        ->middleware('auth.rate.limit:email_verification')
        ->name('resend-verification');

    // Login with rate limiting and lockout protection
    Route::post('login', [AuthController::class, 'login'])
        ->middleware('auth.rate.limit:login')
        ->name('login');

    // Password Reset with rate limiting
    Route::post('forgot-password', [AuthController::class, 'forgotPassword'])
        ->middleware('auth.rate.limit:password_reset')
        ->name('password.forgot');

    Route::post('reset-password', [AuthController::class, 'resetPassword'])
        ->middleware('auth.rate.limit:password_reset')
        ->name('password.reset');

    // OAuth providers list
    Route::get('oauth/providers', [AuthController::class, 'getEnabledProviders'])
        ->name('oauth.providers');

});

// Protected authentication routes (require JWT)
Route::middleware(['auth:api'])->prefix('v1/auth')->name('auth.')->group(function () {
    // Logout and token management
    Route::post('logout', [AuthController::class, 'logout'])->name('logout');
    Route::post('refresh', [AuthController::class, 'refresh'])->name('refresh');

    // User profile
    Route::get('me', [ProfileController::class, 'me'])->name('me');
    Route::put('profile', [ProfileController::class, 'profile'])->name('profile.update');

    // Password change
    Route::post('password/change', [ProfileController::class, 'changePassword'])->name('password.change');
    Route::post('password/confirm-change', [ProfileController::class, 'confirmChangePassword'])->name('password.confirm-change');

    // OAuth account management
    Route::prefix('oauth')->name('oauth.')->group(function () {
        Route::get('accounts', [OAuthController::class, 'getLinkedAccounts'])->name('accounts');
        Route::get('available', [OAuthController::class, 'getAvailableProviders'])->name('available');
        Route::post('link/{provider}', [OAuthController::class, 'linkProvider'])->name('link');
        Route::delete('unlink/{provider}', [OAuthController::class, 'unlinkProvider'])->name('unlink');
    });
});

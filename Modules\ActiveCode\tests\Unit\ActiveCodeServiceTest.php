<?php

namespace Modules\ActiveCode\Tests\Unit;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Modules\ActiveCode\Models\ActiveCode;
use Modules\ActiveCode\Services\ActiveCodeService;
use Modules\ActiveCode\Tests\TestCase;
use Modules\Setting\Facades\SettingFacade;
use PHPUnit\Framework\Attributes\Test;
use Mockery;

/**
 * Unit tests for ActiveCodeService
 *
 * Test all service methods with various scenarios
 */
class ActiveCodeServiceTest extends TestCase
{
    use RefreshDatabase;

    private ActiveCodeService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new ActiveCodeService();
        $this->mockSettingFacade();
    }

    #[Test]
    public function test_generate_verification_code_successfully()
    {
        $identifier = '<EMAIL>';
        $type = 'verification';

        $activeCode = $this->service->generate($identifier, $type);

        $this->assertInstanceOf(ActiveCode::class, $activeCode);
        $this->assertEquals($identifier, $activeCode->identifier);
        $this->assertEquals($type, $activeCode->type);
        $this->assertEquals(6, strlen($activeCode->code));
        $this->assertTrue($activeCode->expires_at->isFuture());
        $this->assertNull($activeCode->used_at);
        $this->assertEquals(0, $activeCode->attempts);
        $this->assertDatabaseHas('active_codes', [
            'identifier' => $identifier,
            'type' => $type,
            'code' => $activeCode->code
        ]);
    }

    #[Test]
    public function test_generate_code_with_custom_expiry_time()
    {
        $identifier = '<EMAIL>';
        $type = 'verification';
        $customExpiry = 30;

        $activeCode = $this->service->generate($identifier, $type, $customExpiry);

        $expectedExpiry = now()->addMinutes($customExpiry);
        $this->assertTrue($activeCode->expires_at->diffInMinutes($expectedExpiry) < 1);
    }

    #[Test]
    public function test_generate_new_code_removes_old_unused_code()
    {
        $identifier = '<EMAIL>';
        $type = 'verification';

        // Create old unused code
        $oldCode = ActiveCode::factory()->create([
            'identifier' => $identifier,
            'type' => $type,
            'used_at' => null
        ]);

        // Generate new code
        $newCode = $this->service->generate($identifier, $type);

        // Check old code was deleted
        $this->assertDatabaseMissing('active_codes', ['id' => $oldCode->id]);
        $this->assertDatabaseHas('active_codes', ['id' => $newCode->id]);
    }

    #[Test]
    public function test_generate_new_code_does_not_remove_used_code()
    {
        $identifier = '<EMAIL>';
        $type = 'verification';

        // Create old used code
        $usedCode = ActiveCode::factory()->used()->create([
            'identifier' => $identifier,
            'type' => $type
        ]);

        // Generate new code
        $newCode = $this->service->generate($identifier, $type);

        // Check used code still exists
        $this->assertDatabaseHas('active_codes', ['id' => $usedCode->id]);
        $this->assertDatabaseHas('active_codes', ['id' => $newCode->id]);
    }

    #[Test]
    public function test_verify_code_successfully()
    {
        $code = '123456';
        $identifier = '<EMAIL>';
        $type = 'verification';

        $activeCode = ActiveCode::factory()->valid()->create([
            'code' => $code,
            'identifier' => $identifier,
            'type' => $type,
            'attempts' => 0
        ]);

        $result = $this->service->verify($code, $identifier, $type);

        $this->assertTrue($result['success']);
        $this->assertEquals('Verification successful', $result['message']);
        $this->assertInstanceOf(ActiveCode::class, $result['code']);

        // Check code was marked as used
        $activeCode->refresh();
        $this->assertNotNull($activeCode->used_at);
        $this->assertEquals(1, $activeCode->attempts);
    }

    #[Test]
    public function test_verify_non_existent_code()
    {
        $result = $this->service->verify('999999', '<EMAIL>', 'verification');

        $this->assertFalse($result['success']);
        $this->assertEquals('Invalid or expired code', $result['message']);
        $this->assertArrayNotHasKey('code', $result);
    }

    #[Test]
    public function test_verify_expired_code()
    {
        $code = '123456';
        $identifier = '<EMAIL>';
        $type = 'verification';

        ActiveCode::factory()->expired()->create([
            'code' => $code,
            'identifier' => $identifier,
            'type' => $type
        ]);

        $result = $this->service->verify($code, $identifier, $type);

        $this->assertFalse($result['success']);
        $this->assertEquals('Invalid or expired code', $result['message']);
    }

    #[Test]
    public function test_verify_used_code()
    {
        $code = '123456';
        $identifier = '<EMAIL>';
        $type = 'verification';

        ActiveCode::factory()->used()->create([
            'code' => $code,
            'identifier' => $identifier,
            'type' => $type
        ]);

        $result = $this->service->verify($code, $identifier, $type);

        $this->assertFalse($result['success']);
        $this->assertEquals('Invalid or expired code', $result['message']);
    }

    #[Test]
    public function test_verify_code_exceeds_max_attempts()
    {
        $code = '123456';
        $identifier = '<EMAIL>';
        $type = 'verification';

        ActiveCode::factory()->valid()->withAttempts(5)->create([
            'code' => $code,
            'identifier' => $identifier,
            'type' => $type
        ]);

        $result = $this->service->verify($code, $identifier, $type);

        $this->assertFalse($result['success']);
        $this->assertEquals('Maximum verification attempts exceeded', $result['message']);
    }

    #[Test]
    public function test_verify_code_increments_attempts_on_failure()
    {
        $code = '123456';
        $identifier = '<EMAIL>';
        $type = 'verification';

        $activeCode = ActiveCode::factory()->valid()->withAttempts(3)->create([
            'code' => $code,
            'identifier' => $identifier,
            'type' => $type
        ]);

        $result = $this->service->verify($code, $identifier, $type);

        $this->assertFalse($result['success']);
        $activeCode->refresh();
        $this->assertEquals(4, $activeCode->attempts);
    }

    #[Test]
    public function test_resend_code_successfully()
    {
        $identifier = '<EMAIL>';
        $type = 'verification';

        // Create old code past rate limit time
        ActiveCode::factory()->create([
            'identifier' => $identifier,
            'type' => $type,
            'created_at' => now()->subMinutes(2)
        ]);

        $result = $this->service->resend($identifier, $type);

        $this->assertTrue($result['success']);
        $this->assertInstanceOf(ActiveCode::class, $result['code']);
        $this->assertEquals($identifier, $result['code']->identifier);
        $this->assertEquals($type, $result['code']->type);
    }

    #[Test]
    public function test_resend_code_rate_limited()
    {
        $identifier = '<EMAIL>';
        $type = 'verification';

        // Create new code just created (within rate limit)
        ActiveCode::factory()->create([
            'identifier' => $identifier,
            'type' => $type,
            'created_at' => now()
        ]);

        $result = $this->service->resend($identifier, $type);

        $this->assertFalse($result['success']);
        $this->assertStringContains('Please wait', $result['message']);
        $this->assertStringContains('seconds before requesting', $result['message']);
    }

    #[Test]
    public function test_resend_code_when_no_existing_code()
    {
        $identifier = '<EMAIL>';
        $type = 'verification';

        $result = $this->service->resend($identifier, $type);

        $this->assertTrue($result['success']);
        $this->assertInstanceOf(ActiveCode::class, $result['code']);
    }

    #[Test]
    public function test_cleanup_expired_codes()
    {
        // Create expired code (past cleanup time)
        $expiredCode = ActiveCode::factory()->create([
            'expires_at' => now()->subDays(2)
        ]);

        // Create valid code
        $validCode = ActiveCode::factory()->valid()->create();

        // Create expired code but not yet cleanup time
        $recentExpiredCode = ActiveCode::factory()->create([
            'expires_at' => now()->subHours(1)
        ]);

        $this->service->cleanup();

        // Check old expired code was deleted
        $this->assertDatabaseMissing('active_codes', ['id' => $expiredCode->id]);
        // Check valid code still exists
        $this->assertDatabaseHas('active_codes', ['id' => $validCode->id]);
        // Check recent expired code still exists
        $this->assertDatabaseHas('active_codes', ['id' => $recentExpiredCode->id]);
    }

    #[Test]
    public function test_clear_settings_cache_successfully()
    {
        Cache::shouldReceive('tags')
            ->with('activecode_settings')
            ->once()
            ->andReturnSelf();

        Cache::shouldReceive('flush')
            ->once()
            ->andReturn(true);

        Log::shouldReceive('info')
            ->once()
            ->with('ActiveCode settings cache cleared successfully');

        $this->service->clearSettingsCache();
    }

    #[Test]
    public function test_clear_settings_cache_fails()
    {
        Cache::shouldReceive('tags')
            ->with('activecode_settings')
            ->once()
            ->andReturnSelf();

        Cache::shouldReceive('flush')
            ->once()
            ->andThrow(new \Exception('Cache error'));

        Log::shouldReceive('error')
            ->once()
            ->with('Failed to clear ActiveCode settings cache', ['error' => 'Cache error']);

        $this->service->clearSettingsCache();
    }

    #[Test]
    public function test_warm_settings_cache_successfully()
    {
        Log::shouldReceive('info')
            ->twice(); // Once for loading, once for warmed up

        $this->service->warmSettingsCache();
    }

    #[Test]
    public function test_warm_settings_cache_fails()
    {
        // Mock SettingFacade to throw exception
        SettingFacade::shouldReceive('getSettings')
            ->andThrow(new \Exception('Database error'));

        Log::shouldReceive('error')
            ->once()
            ->with('Failed to warm up ActiveCode settings cache', ['error' => 'Database error']);

        $this->service->warmSettingsCache();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}

<?php

namespace Modules\Role\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Core\Traits\ResponseTrait;
use Modules\Role\Http\Filters\RoleFilter;
use Modules\Role\Http\Requests\BulkRoleRequest;
use Modules\Role\Http\Requests\BulkRoleDestroyRequest;
use Modules\Role\Http\Requests\RoleRequest;
use Modules\Role\Models\Role;

class RoleController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {
        $this->middleware('role_or_permission:super-admin|role.view')->only(['index', 'show', 'dropdown']);
        $this->middleware('role_or_permission:super-admin|role.create')->only(['store']);
        $this->middleware('role_or_permission:super-admin|role.edit')->only(['update']);
        $this->middleware('role_or_permission:super-admin|role.delete')->only(['delete', 'restore', 'bulkDelete', 'bulkRestore']);
        $this->middleware('role_or_permission:super-admin|role.destroy')->only([ 'destroy', 'bulkDestroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $roles = Role::query()
            ->with(['permissions:id,name,display_name'])
            ->filter(new RoleFilter($request))
            ->orderBy('name')
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($roles, __('Roles retrieved successfully.'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(RoleRequest $request): JsonResponse
    {
        try {
            $role = Role::create($request->all());
            $role->syncPermissions($request->input('permissions', []));
            return $this->successResponse($role, __('Role created successfully.'), 201);
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to create role.'));
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id): JsonResponse
    {
        $role = Role::withTrashed()->findOrFail($id);
        $role->load(['permissions']);
        return $this->successResponse($role, __('Role retrieved successfully.'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(RoleRequest $request, int $id): JsonResponse
    {
        try {
            $role = Role::withTrashed()->findOrFail($id);
            $role->update($request->all());
            $role->syncPermissions($request->input('permissions', []));
            return $this->successResponse($role->fresh(), __('Role updated successfully.'));
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to update role.'));
        }
    }

    /**
     * Soft delete the specified resource.
     */
    public function delete(int $id): JsonResponse
    {
        try {
            $role = Role::withTrashed()->findOrFail($id);

            if (!$role->canBeDeleted()) {
                return $this->errorResponse(
                    null,
                    __('Cannot delete this role. It may be a system role or have assigned users.'),
                    422
                );
            }

            $role->delete();

            return $this->successResponse(null, __('Role deleted successfully.'));
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to delete role.'));
        }
    }

    /**
     * Restore the specified role from trash.
     */
    public function restore(int $id): JsonResponse
    {
        try {
            $role = Role::onlyTrashed()->findOrFail($id);
            $role->restore();

            return $this->successResponse($role->fresh(), __('Role restored successfully.'));
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to restore role.'));
        }
    }

    /**
     * Permanently delete the specified role.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $role = Role::withTrashed()->findOrFail($id);

            if (!$role->canBeDeleted()) {
                return $this->errorResponse(
                    null,
                    __('Cannot permanently delete this role. It may be a system role or have assigned users.'),
                    422
                );
            }

            $role->forceDelete();

            return $this->successResponse(null, __('Role permanently deleted successfully.'));
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, __('Failed to permanently delete role.'));
        }
    }

    /**
     * Bulk soft delete roles.
     */
    public function bulkDelete(BulkRoleRequest $request): JsonResponse
    {
        $ids = $request->input('ids');
        $roles = Role::whereIn('id', $ids)->get();

        $deletedCount = 0;
        $errors = [];

        foreach ($roles as $role) {
            if ($role->canBeDeleted()) {
                $role->delete();
                $deletedCount++;
            } else {
                $errors[] = "Role '{$role->display_name}' cannot be deleted.";
            }
        }

        $message = __(':count roles deleted successfully.', ['count' => $deletedCount]);

        if (!empty($errors)) {
            $message .= ' ' . __('Some roles could not be deleted: :errors', ['errors' => implode(', ', $errors)]);
        }

        return $this->successResponse(['deleted_count' => $deletedCount, 'errors' => $errors], $message);
    }

    /**
     * Bulk restore roles.
     */
    public function bulkRestore(BulkRoleRequest $request): JsonResponse
    {
        $ids = $request->input('ids');
        $restoredCount = Role::onlyTrashed()->whereIn('id', $ids)->restore();

        return $this->successResponse(
            ['restored_count' => $restoredCount],
            __('Roles restored successfully.')
        );
    }

    /**
     * Bulk permanently delete roles.
     */
    public function bulkDestroy(BulkRoleDestroyRequest $request): JsonResponse
    {
        $ids = $request->input('ids');
        $roles = Role::withTrashed()->whereIn('id', $ids)->get();

        $deletedCount = 0;
        $errors = [];

        foreach ($roles as $role) {
            if ($role->canBeDeleted()) {
                $role->forceDelete();
                $deletedCount++;
            } else {
                $errors[] = "Role '{$role->display_name}' cannot be permanently deleted.";
            }
        }


        $message = __(':count roles permanently deleted.', ['count' => $deletedCount]);

        if (!empty($errors)) {
            $message .= ' ' . __('Some roles could not be deleted: :errors', ['errors' => implode(', ', $errors)]);
        }

        return $this->successResponse(['deleted_count' => $deletedCount, 'errors' => $errors], $message);
    }

    /**
     * Get roles for dropdown.
     */
    public function dropdown(): JsonResponse
    {
        $roles = Role::query()->active()->byPriority()->get();
        return $this->successResponse(
            $roles->makeHidden(['updated_at', 'created_at', 'status']),
            __('Roles retrieved successfully.')
        );
    }
}

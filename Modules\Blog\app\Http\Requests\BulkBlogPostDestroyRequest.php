<?php

namespace Modules\Blog\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

class BulkBlogPostDestroyRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ids' => [
                'required',
                'array',
                'min:1',
            ],
            'ids.*' => [
                'required',
                'integer',
                Rule::exists('blog_posts', 'id')->whereNotNull('deleted_at'), // Only soft deleted posts
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'ids' => __('Blog Post IDs'),
            'ids.*' => __('Blog Post ID'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ids.required' => __('Please select at least one blog post.'),
            'ids.array' => __('Invalid data format.'),
            'ids.min' => __('Please select at least one blog post.'),
            'ids.*.required' => __('Blog Post ID is required.'),
            'ids.*.integer' => __('Blog Post ID must be an integer.'),
            'ids.*.exists' => __('One or more selected blog posts must be in trash to be permanently deleted.'),
        ];
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_social_accounts', function (Blueprint $table) {
            $table->id();
            
            // Foreign key to users table
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            
            // OAuth provider information
            $table->string('provider', 50)->index(); // 'google', 'facebook', 'twitter', etc.
            $table->string('provider_id')->index(); // ID from OAuth provider
            $table->string('provider_email')->nullable(); // Email from provider (may differ from user email)
            $table->string('provider_name')->nullable(); // Display name from provider
            $table->string('provider_nickname')->nullable(); // Username/handle from provider
            $table->text('provider_avatar')->nullable(); // Avatar URL from provider
            
            // OAuth tokens (optional - for future use)
            $table->text('access_token')->nullable();
            $table->text('refresh_token')->nullable();
            $table->timestamp('token_expires_at')->nullable();
            
            // Additional provider data (JSON for flexibility)
            $table->json('provider_data')->nullable()->comment('Additional data from OAuth provider');
            
            // Tracking
            $table->timestamp('last_used_at')->nullable()->comment('Last time this OAuth account was used for login');
            $table->timestamps();
            
            // Indexes and constraints
            $table->unique(['provider', 'provider_id'], 'unique_provider_account');
            $table->index(['user_id', 'provider'], 'idx_user_provider');
            $table->index(['provider_email'], 'idx_provider_email');
            $table->index(['last_used_at'], 'idx_last_used');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_social_accounts');
    }
};

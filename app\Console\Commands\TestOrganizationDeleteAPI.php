<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\Organization\Models\Organization;
use Modules\User\Models\User;
use <PERSON>mon\JWTAuth\Facades\JWTAuth;

class TestOrganizationDeleteAPI extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:organization-delete-api {user_id} {organization_id} {--route=organization}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test organization deletion via API';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id');
        $organizationId = $this->argument('organization_id');
        $routeType = $this->option('route'); // 'organization' or 'space'
        
        $user = User::find($userId);
        // Try to find by ID first, then by UUID
        $organization = is_numeric($organizationId)
            ? Organization::find($organizationId)
            : Organization::where('uuid', $organizationId)->first();
        
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return;
        }
        
        if (!$organization) {
            $this->error("Organization with ID {$organizationId} not found");
            return;
        }

        $this->info("Testing organization deletion via API:");
        $this->info("User: {$user->email} (ID: {$user->id})");
        $this->info("Organization: {$organization->name} (ID: {$organization->id})");
        $this->info("Route type: {$routeType}");
        
        try {
            // Generate JWT token for user
            $token = JWTAuth::fromUser($user);
            
            // Determine endpoint based on route type
            if ($routeType === 'space') {
                $endpoint = "/api/v1/auth/spaces/{$organizationId}";
            } else {
                $endpoint = "/api/v1/auth/organizations/{$organizationId}";
            }
            
            $this->info("Making DELETE request to: {$endpoint}");
            
            // Test API endpoint using HTTP client
            $response = $this->makeAPIRequest($endpoint, $token, 'DELETE');
            
            $this->info("API Response Status: " . $response['status']);
            $this->info("API Response Body: " . $response['body']);
            
            if ($response['status'] === 200) {
                $this->info("✓ Organization deleted successfully via API");
            } elseif ($response['status'] === 403) {
                $this->error("✗ Access denied - User doesn't have permission to delete this organization");
            } elseif ($response['status'] === 404) {
                $this->error("✗ Organization not found");
            } else {
                $this->error("✗ Unexpected response status: " . $response['status']);
            }
            
        } catch (\Exception $e) {
            $this->error("✗ Failed to test API:");
            $this->error($e->getMessage());
        }
    }
    
    private function makeAPIRequest($endpoint, $token, $method = 'GET')
    {
        $baseUrl = 'http://localhost:8000';
        $url = $baseUrl . $endpoint;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new \Exception("CURL Error: " . $error);
        }
        
        return [
            'status' => $httpCode,
            'body' => $response
        ];
    }
}

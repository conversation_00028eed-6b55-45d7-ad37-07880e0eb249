<?php

namespace Modules\ActiveCode\Tests\Integration;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Modules\ActiveCode\Facades\NotificationFacade;
use Modules\ActiveCode\Models\ActiveCode;
use Modules\ActiveCode\Services\ActiveCodeService;
use Modules\ActiveCode\Tests\TestCase;
use Modules\Setting\Facades\SettingFacade;
use PHPUnit\Framework\Attributes\Test;
use Mockery;

/**
 * Integration tests for ActiveCode module
 *
 * Test interactions between components: Service, Model, Facade, Cache, Settings
 */
class ActiveCodeIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->mockSettingFacade();
    }

    #[Test]
    public function test_complete_code_generation_and_verification_workflow()
    {
        $identifier = '<EMAIL>';
        $type = 'email_verification';

        // Step 1: Generate code through Facade
        $activeCode = NotificationFacade::generate($identifier, $type);

        $this->assertInstanceOf(ActiveCode::class, $activeCode);
        $this->assertEquals($identifier, $activeCode->identifier);
        $this->assertEquals($type, $activeCode->type);
        $this->assertTrue($activeCode->isValid());

        // Step 2: Verify code through Facade
        $result = NotificationFacade::verify($activeCode->code, $identifier, $type);

        $this->assertTrue($result['success']);
        $this->assertEquals('Verification successful', $result['message']);

        // Step 3: Check code was marked as used
        $activeCode->refresh();
        $this->assertFalse($activeCode->isValid());
        $this->assertNotNull($activeCode->used_at);
        $this->assertEquals(1, $activeCode->attempts);
    }

    #[Test]
    public function test_resend_workflow_with_rate_limiting()
    {
        $identifier = '<EMAIL>';
        $type = 'verification';

        // Step 1: Generate first code
        $firstCode = NotificationFacade::generate($identifier, $type);

        // Step 2: Try resend immediately (will be rate limited)
        $resendResult = NotificationFacade::resend($identifier, $type);
        $this->assertFalse($resendResult['success']);
        $this->assertStringContains('Please wait', $resendResult['message']);

        // Step 3: Simulate time past rate limit
        $firstCode->update(['created_at' => now()->subMinutes(2)]);

        // Step 4: Resend successfully
        $resendResult = NotificationFacade::resend($identifier, $type);
        $this->assertTrue($resendResult['success']);
        $this->assertInstanceOf(ActiveCode::class, $resendResult['code']);

        // Step 5: Check old code was deleted
        $this->assertDatabaseMissing('active_codes', ['id' => $firstCode->id]);
        $this->assertDatabaseHas('active_codes', ['id' => $resendResult['code']->id]);
    }

    #[Test]
    public function test_integration_with_cache_system()
    {
        // Mock cache to track calls
        Cache::shouldReceive('tags')
            ->with('activecode_settings')
            ->andReturnSelf();

        Cache::shouldReceive('remember')
            ->once()
            ->andReturn(collect(['activecode_expiry_minutes' => 15]));

        $service = new ActiveCodeService();
        $code = $service->generate('<EMAIL>', 'verification');

        $this->assertInstanceOf(ActiveCode::class, $code);
    }

    #[Test]
    public function test_integration_with_settings_system()
    {
        // Mock custom settings
        $customSettings = [
            'activecode_expiry_minutes' => 30,
            'activecode_length' => 8,
            'activecode_max_attempts' => 3
        ];

        $this->mockSettingFacade($customSettings);

        $service = new ActiveCodeService();
        $code = $service->generate('<EMAIL>', 'verification');

        // Check code length according to setting
        $this->assertEquals(8, strlen($code->code));

        // Check expiry time according to setting
        $expectedExpiry = now()->addMinutes(30);
        $this->assertTrue($code->expires_at->diffInMinutes($expectedExpiry) < 1);
    }

    #[Test]
    public function test_error_handling_when_settings_unavailable()
    {
        // Mock SettingFacade throw exception
        SettingFacade::shouldReceive('getSettings')
            ->andThrow(new \Exception('Database connection failed'));

        Log::shouldReceive('error')->atLeast()->once();

        $service = new ActiveCodeService();

        // Service still works with default values
        $code = $service->generate('<EMAIL>', 'verification');

        $this->assertInstanceOf(ActiveCode::class, $code);
        $this->assertEquals(6, strlen($code->code)); // Default length
    }

    #[Test]
    public function test_automatic_cleanup_with_scheduled_task()
    {
        // Create codes with different times
        $oldExpiredCode = ActiveCode::factory()->create([
            'expires_at' => now()->subDays(3)
        ]);

        $recentExpiredCode = ActiveCode::factory()->create([
            'expires_at' => now()->subHours(1)
        ]);

        $validCode = ActiveCode::factory()->valid()->create();

        // Run cleanup
        NotificationFacade::cleanup();

        // Check only old expired code was deleted
        $this->assertDatabaseMissing('active_codes', ['id' => $oldExpiredCode->id]);
        $this->assertDatabaseHas('active_codes', ['id' => $recentExpiredCode->id]);
        $this->assertDatabaseHas('active_codes', ['id' => $validCode->id]);
    }

    #[Test]
    public function test_concurrent_verification_attempts_handling()
    {
        $code = '123456';
        $identifier = '<EMAIL>';
        $type = 'verification';

        $activeCode = ActiveCode::factory()->valid()->withAttempts(4)->create([
            'code' => $code,
            'identifier' => $identifier,
            'type' => $type
        ]);

        // Simulate 2 concurrent verify requests
        $result1 = NotificationFacade::verify($code, $identifier, $type);
        $result2 = NotificationFacade::verify($code, $identifier, $type);

        // Only one will succeed (due to max attempts exceeded)
        $this->assertFalse($result1['success']);
        $this->assertFalse($result2['success']);

        $activeCode->refresh();
        $this->assertGreaterThanOrEqual(5, $activeCode->attempts);
    }

    #[Test]
    public function test_integration_with_multiple_types_and_identifiers()
    {
        $identifiers = [
            '<EMAIL>',
            '<EMAIL>',
            '+84123456789'
        ];

        $types = [
            'email_verification',
            'phone_verification',
            'two_factor_auth'
        ];

        $codes = [];

        // Create code for each combination
        foreach ($identifiers as $identifier) {
            foreach ($types as $type) {
                $codes[] = NotificationFacade::generate($identifier, $type);
            }
        }

        $this->assertCount(9, $codes);
        $this->assertDatabaseCount('active_codes', 9);

        // Verify each code
        foreach ($codes as $code) {
            $result = NotificationFacade::verify(
                $code->code,
                $code->identifier,
                $code->type
            );
            $this->assertTrue($result['success']);
        }
    }

    #[Test]
    public function test_performance_with_large_dataset()
    {
        // Create many expired codes
        ActiveCode::factory()->count(100)->expired()->create();

        // Create some valid codes
        ActiveCode::factory()->count(10)->valid()->create();

        $startTime = microtime(true);

        // Run cleanup
        NotificationFacade::cleanup();

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // Cleanup should complete in reasonable time (< 1 second)
        $this->assertLessThan(1.0, $executionTime);

        // Check only 10 valid codes remain
        $this->assertDatabaseCount('active_codes', 10);
    }

    #[Test]
    public function test_cache_warming_and_clearing_integration()
    {
        Cache::shouldReceive('tags')
            ->with('activecode_settings')
            ->andReturnSelf();

        Cache::shouldReceive('flush')
            ->once()
            ->andReturn(true);

        Cache::shouldReceive('remember')
            ->once()
            ->andReturn(collect());

        Log::shouldReceive('info')->atLeast()->once();

        // Clear cache
        NotificationFacade::clearSettingsCache();

        // Warm cache
        NotificationFacade::warmSettingsCache();
    }

    #[Test]
    public function test_end_to_end_workflow_with_error_handling()
    {
        $identifier = '<EMAIL>';
        $type = 'verification';

        // 1. Generate code successfully
        $code = NotificationFacade::generate($identifier, $type);
        $this->assertInstanceOf(ActiveCode::class, $code);

        // 2. Verify with wrong code
        $result = NotificationFacade::verify('wrong_code', $identifier, $type);
        $this->assertFalse($result['success']);

        // 3. Verify with correct code
        $result = NotificationFacade::verify($code->code, $identifier, $type);
        $this->assertTrue($result['success']);

        // 4. Try to verify used code again
        $result = NotificationFacade::verify($code->code, $identifier, $type);
        $this->assertFalse($result['success']);
        $this->assertEquals('Invalid or expired code', $result['message']);

        // 5. Resend new code
        $code->update(['created_at' => now()->subMinutes(2)]);
        $resendResult = NotificationFacade::resend($identifier, $type);
        $this->assertTrue($resendResult['success']);

        // 6. Cleanup
        NotificationFacade::cleanup();

        // New code still exists
        $this->assertDatabaseHas('active_codes', [
            'id' => $resendResult['code']->id
        ]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}

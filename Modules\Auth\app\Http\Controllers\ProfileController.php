<?php

namespace Modules\Auth\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\Auth\Facades\AuthFacade;
use Modules\Auth\Http\Requests\ChangePasswordRequest;
use Modules\Auth\Http\Requests\ConfirmChangePasswordRequest;
use Modules\Auth\Http\Requests\ForgotPasswordRequest;
use Modules\Auth\Http\Requests\ProfileRequest;
use Modules\Auth\Http\Requests\ResetPasswordRequest;
use Modules\Core\Traits\ResponseTrait;
use Throwable;

class ProfileController extends Controller
{
    use ResponseTrait;
    /**
     * Get current authenticated user.
     */
    public function me(): JsonResponse
    {
        $user = Auth::user();
        $user->load(['geoDivision', 'country', 'roles']);

        return $this->successResponse(
            $user,
            __('User information retrieved successfully.')
        );
    }

    /**
     * Update user profile.
     */
    public function profile(ProfileRequest $request): JsonResponse
    {
        try {
            $user = AuthFacade::updateProfile($request->all());

            return $this->successResponse(
                $user,
                __('Profile updated successfully.')
            );
        } catch (Throwable $e) {
            return $this->errorResponse(
                null,
                __('Profile update failed: :error', ['error' => $e->getMessage()]),
                500
            );
        }
    }

    /**
     * Request OTP for password change.
     */
    public function changePassword(ChangePasswordRequest $request): JsonResponse
    {
        $result = AuthFacade::requestPasswordChange(
            $request->input('current_password')
        );

        if (is_string($result)) {
            return $this->errorResponse(null, $result, 400);
        }

        return $this->successResponse(
            null,
            __('Password change code sent to your email.')
        );
    }

    /**
     * Confirm password change with OTP.
     */
    public function confirmChangePassword(ConfirmChangePasswordRequest $request): JsonResponse
    {
        $result = AuthFacade::confirmPasswordChange(
            $request->input('code'),
            $request->input('new_password')
        );

        if (is_string($result)) {
            return $this->errorResponse(null, $result, 400);
        }

        return $this->successResponse(
            null,
            __('Password changed successfully.')
        );
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add 'processing' to the status enum
        DB::statement("ALTER TABLE messages MODIFY COLUMN status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'completed'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove 'processing' from the status enum
        DB::statement("ALTER TABLE messages MODIFY COLUMN status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'completed'");
    }
};

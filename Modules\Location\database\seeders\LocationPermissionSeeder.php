<?php

namespace Modules\Location\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Role\Models\Permission;
use Modules\Role\Models\Role;

class LocationPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedLocationPermissions();
        $this->assignPermissionsToSuperAdmin();
    }

    /**
     * Seed location module permissions.
     */
    private function seedLocationPermissions(): void
    {
        $permissions = [
            // Country Permissions
            [
                'name' => 'location.country.view',
                'display_name' => 'View Countries',
                'description' => 'Permission to view countries list and details',
                'module_name' => 'location',
                'sort_order' => 1,
                'guard_name' => 'api',
            ],
            [
                'name' => 'location.country.create',
                'display_name' => 'Create Countries',
                'description' => 'Permission to create new countries',
                'module_name' => 'location',
                'sort_order' => 2,
                'guard_name' => 'api',
            ],
            [
                'name' => 'location.country.edit',
                'display_name' => 'Edit Countries',
                'description' => 'Permission to update existing countries',
                'module_name' => 'location',
                'sort_order' => 3,
                'guard_name' => 'api',
            ],
            [
                'name' => 'location.country.delete',
                'display_name' => 'Delete Countries',
                'description' => 'Permission to soft delete and restore countries',
                'module_name' => 'location',
                'sort_order' => 4,
                'guard_name' => 'api',
            ],
            [
                'name' => 'location.country.destroy',
                'display_name' => 'Destroy Countries',
                'description' => 'Permission to permanently delete countries',
                'module_name' => 'location',
                'sort_order' => 5,
                'guard_name' => 'api',
            ],

            // GeoDivision Permissions
            [
                'name' => 'location.geodivision.view',
                'display_name' => 'View Geographic Divisions',
                'description' => 'Permission to view geographic divisions (states, cities, districts) list and details',
                'module_name' => 'location',
                'sort_order' => 6,
                'guard_name' => 'api',
            ],
            [
                'name' => 'location.geodivision.create',
                'display_name' => 'Create Geographic Divisions',
                'description' => 'Permission to create new geographic divisions',
                'module_name' => 'location',
                'sort_order' => 7,
                'guard_name' => 'api',
            ],
            [
                'name' => 'location.geodivision.edit',
                'display_name' => 'Edit Geographic Divisions',
                'description' => 'Permission to update existing geographic divisions',
                'module_name' => 'location',
                'sort_order' => 8,
                'guard_name' => 'api',
            ],
            [
                'name' => 'location.geodivision.delete',
                'display_name' => 'Delete Geographic Divisions',
                'description' => 'Permission to soft delete and restore geographic divisions',
                'module_name' => 'location',
                'sort_order' => 9,
                'guard_name' => 'api',
            ],
            [
                'name' => 'location.geodivision.destroy',
                'display_name' => 'Destroy Geographic Divisions',
                'description' => 'Permission to permanently delete geographic divisions',
                'module_name' => 'location',
                'sort_order' => 10,
                'guard_name' => 'api',
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => 'api'],
                array_merge($permission, ['guard_name' => 'api'])
            );
        }
    }

    /**
     * Assign location permissions to super-admin role.
     */
    private function assignPermissionsToSuperAdmin(): void
    {
        $superAdmin = Role::where('name', 'super-admin')->where('guard_name', 'api')->first();

        if ($superAdmin) {
            $locationPermissions = Permission::where('module_name', 'location')->where('guard_name', 'api')->get();
            
            // Gán tất cả quyền location cho super-admin
            foreach ($locationPermissions as $permission) {
                if (!$superAdmin->hasPermissionTo($permission->name)) {
                    $superAdmin->givePermissionTo($permission->name);
                }
            }
        }
    }
}

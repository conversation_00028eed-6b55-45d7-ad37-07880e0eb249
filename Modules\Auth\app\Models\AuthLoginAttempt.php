<?php

namespace Modules\Auth\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

/**
 * AuthLoginAttempt Model
 * 
 * Tracks authentication attempts for security monitoring and rate limiting.
 */
class AuthLoginAttempt extends Model
{
    use HasFactory;

    protected $table = 'auth_login_attempts';

    protected $fillable = [
        'identifier',
        'ip_address',
        'user_agent',
        'action',
        'successful',
        'failure_reason',
        'attempted_at',
        'additional_data',
    ];

    protected $casts = [
        'successful' => 'boolean',
        'attempted_at' => 'datetime',
        'additional_data' => 'array',
    ];

    /**
     * Scope for failed attempts.
     */
    public function scopeFailed($query)
    {
        return $query->where('successful', false);
    }

    /**
     * Scope for successful attempts.
     */
    public function scopeSuccessful($query)
    {
        return $query->where('successful', true);
    }

    /**
     * Scope for specific action.
     */
    public function scopeAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope for specific identifier.
     */
    public function scopeIdentifier($query, string $identifier)
    {
        return $query->where('identifier', $identifier);
    }

    /**
     * Scope for specific IP address.
     */
    public function scopeIpAddress($query, string $ipAddress)
    {
        return $query->where('ip_address', $ipAddress);
    }

    /**
     * Scope for recent attempts within specified minutes.
     */
    public function scopeRecent($query, int $minutes = 60)
    {
        return $query->where('attempted_at', '>=', now()->subMinutes($minutes));
    }

    /**
     * Scope for attempts within date range.
     */
    public function scopeBetweenDates($query, Carbon $startDate, Carbon $endDate)
    {
        return $query->whereBetween('attempted_at', [$startDate, $endDate]);
    }

    /**
     * Get failed attempts count for identifier and action within time window.
     */
    public static function getFailedAttemptsCount(string $identifier, string $action, int $minutes = 60): int
    {
        return static::failed()
            ->action($action)
            ->identifier($identifier)
            ->recent($minutes)
            ->count();
    }

    /**
     * Get recent attempts for identifier.
     */
    public static function getRecentAttempts(string $identifier, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return static::identifier($identifier)
            ->orderBy('attempted_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Clean up old attempts (older than specified days).
     */
    public static function cleanup(int $days = 30): int
    {
        return static::where('attempted_at', '<', now()->subDays($days))->delete();
    }

    /**
     * Get statistics for dashboard.
     */
    public static function getStatistics(int $days = 7): array
    {
        $startDate = now()->subDays($days);
        
        return [
            'total_attempts' => static::betweenDates($startDate, now())->count(),
            'successful_attempts' => static::successful()->betweenDates($startDate, now())->count(),
            'failed_attempts' => static::failed()->betweenDates($startDate, now())->count(),
            'unique_ips' => static::betweenDates($startDate, now())->distinct('ip_address')->count(),
            'login_attempts' => static::action('login')->betweenDates($startDate, now())->count(),
            'register_attempts' => static::action('register')->betweenDates($startDate, now())->count(),
            'password_reset_attempts' => static::action('password_reset')->betweenDates($startDate, now())->count(),
        ];
    }
}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\Organization\Models\Organization;
use Modules\User\Models\User;

class TestOrganizationCreation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:organization-creation {user_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test organization creation for a user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id') ?? 1;
        
        $user = User::find($userId);
        
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return;
        }

        $this->info("Testing organization creation for user: {$user->email}");
        
        try {
            // Test data
            $data = [
                'name' => 'Test Organization ' . now()->format('Y-m-d H:i:s'),
                'type' => 'team',
                'email' => '<EMAIL>',
                'description' => 'Test organization created by command',
                'owner_id' => $user->id,
                'status' => 'active',
                'visibility' => 'private'
            ];

            $this->info('Creating organization with data:');
            $this->table(['Field', 'Value'], collect($data)->map(fn($v, $k) => [$k, $v])->toArray());

            $organization = Organization::create($data);
            
            $this->info("✓ Organization created successfully!");
            $this->info("ID: {$organization->id}");
            $this->info("Name: {$organization->name}");
            $this->info("Type: {$organization->type}");
            $this->info("Owner: {$organization->owner_id}");
            
        } catch (\Exception $e) {
            $this->error("✗ Failed to create organization:");
            $this->error($e->getMessage());
            $this->error("File: {$e->getFile()}:{$e->getLine()}");
            
            if ($this->option('verbose')) {
                $this->error("Stack trace:");
                $this->error($e->getTraceAsString());
            }
        }
    }
}

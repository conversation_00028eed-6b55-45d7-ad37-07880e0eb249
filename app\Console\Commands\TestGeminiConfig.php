<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\ModelAI\Models\ModelProvider;
use Modules\ModelAI\Models\ModelAI;
use Gemini\Laravel\Facades\Gemini;
use Illuminate\Support\Facades\Log;
use Exception;

class TestGeminiConfig extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:gemini-config 
                            {--model=gemma-3n-e4b-it : Model key to test}
                            {--prompt= : Custom test prompt}
                            {--debug : Enable debug logging}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Gemini configuration and API key issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Testing Gemini Configuration...');
        $this->newLine();

        $modelKey = $this->option('model');
        $customPrompt = $this->option('prompt') ?: 'Hello, please respond with "Gemini test successful"';
        $debug = $this->option('debug');

        // Step 1: Check Gemini provider
        $this->testGeminiProvider($debug);
        $this->newLine();

        // Step 2: Check specific model
        $this->testGeminiModel($modelKey, $debug);
        $this->newLine();

        // Step 3: Test configuration setting
        $this->testConfigurationSetting($modelKey, $debug);
        $this->newLine();

        // Step 4: Test actual API call
        $this->testGeminiAPICall($modelKey, $customPrompt, $debug);

        return 0;
    }

    /**
     * Test Gemini provider configuration.
     */
    private function testGeminiProvider(bool $debug): void
    {
        $this->info('📋 Step 1: Testing Gemini Provider...');

        $providers = ModelProvider::whereIn('key', ['google', 'gemini'])->get();

        if ($providers->isEmpty()) {
            $this->error('❌ No Gemini/Google providers found');
            return;
        }

        foreach ($providers as $provider) {
            $this->line("   🔍 Provider: {$provider->name} ({$provider->key})");
            $this->line("   📊 Status: " . ($provider->isActive() ? '✅ Active' : '❌ Inactive'));
            
            $apiKey = $provider->getApiKey();
            $this->line("   🔑 API Key: " . ($apiKey ? '✅ Present' : '❌ Missing'));
            
            if ($debug && $apiKey) {
                $this->line("      - Length: " . strlen($apiKey));
                $this->line("      - Prefix: " . substr($apiKey, 0, 4) . '...');
                $this->line("      - Suffix: ..." . substr($apiKey, -4));
            }

            $this->line("   🔐 Valid Credentials: " . ($provider->hasValidCredentials() ? '✅ Yes' : '❌ No'));
        }
    }

    /**
     * Test specific Gemini model.
     */
    private function testGeminiModel(string $modelKey, bool $debug): void
    {
        $this->info("📋 Step 2: Testing Model '{$modelKey}'...");

        $model = ModelAI::where('key', $modelKey)->first();

        if (!$model) {
            $this->error("❌ Model '{$modelKey}' not found");
            return;
        }

        $this->line("   🤖 Model: {$model->name}");
        $this->line("   📊 Status: " . ($model->status === 'active' ? '✅ Active' : '❌ Inactive'));
        $this->line("   🎯 Default: " . ($model->is_default ? '✅ Yes' : '❌ No'));

        $provider = $model->provider;
        $this->line("   🏢 Provider: {$provider->name} ({$provider->key})");

        if ($debug) {
            $this->line("   🔍 Model ID: {$model->id}");
            $this->line("   🔍 Provider ID: {$provider->id}");
        }
    }

    /**
     * Test configuration setting mechanism.
     */
    private function testConfigurationSetting(string $modelKey, bool $debug): void
    {
        $this->info('📋 Step 3: Testing Configuration Setting...');

        $model = ModelAI::where('key', $modelKey)->first();
        if (!$model) {
            $this->error("❌ Model not found");
            return;
        }

        $provider = $model->provider;
        $apiKey = $provider->getApiKey();

        if (!$apiKey) {
            $this->error("❌ No API key available");
            return;
        }

        // Test current config
        $currentConfig = config('gemini.api_key');
        $this->line("   📋 Current config: " . ($currentConfig ? 'Set' : 'Not set'));

        if ($debug && $currentConfig) {
            $this->line("      - Current prefix: " . substr($currentConfig, 0, 4) . '...');
        }

        // Test setting config
        $this->line("   🔧 Setting new config...");
        config()->set(['gemini.api_key' => $apiKey]);

        // Verify config was set
        $newConfig = config('gemini.api_key');
        $configMatches = $newConfig === $apiKey;

        $this->line("   ✅ Config set: " . ($newConfig ? 'Yes' : 'No'));
        $this->line("   🔍 Config matches: " . ($configMatches ? '✅ Yes' : '❌ No'));

        if ($debug) {
            $this->line("      - Expected prefix: " . substr($apiKey, 0, 4) . '...');
            $this->line("      - Actual prefix: " . ($newConfig ? substr($newConfig, 0, 4) . '...' : 'null'));
            $this->line("      - Expected length: " . strlen($apiKey));
            $this->line("      - Actual length: " . strlen($newConfig ?? ''));
        }

        if (!$configMatches) {
            $this->warn("   ⚠️  Configuration mismatch detected!");
        }
    }

    /**
     * Test actual Gemini API call.
     */
    private function testGeminiAPICall(string $modelKey, string $prompt, bool $debug): void
    {
        $this->info('📋 Step 4: Testing Gemini API Call...');

        $model = ModelAI::where('key', $modelKey)->first();
        if (!$model) {
            $this->error("❌ Model not found");
            return;
        }

        $provider = $model->provider;
        $apiKey = $provider->getApiKey();

        if (!$apiKey) {
            $this->error("❌ No API key available");
            return;
        }

        try {
            // Set configuration
            config()->set(['gemini.api_key' => $apiKey]);

            $this->line("   🚀 Making API call...");
            $this->line("   📝 Prompt: {$prompt}");

            if ($debug) {
                $this->line("   🔍 Using model: {$model->key}");
                $this->line("   🔍 API key prefix: " . substr($apiKey, 0, 4) . '...');
            }

            // Make the API call
            $gemini = Gemini::generativeModel(model: $model->key)->generateContent($prompt);
            $response = $gemini->text();

            if ($response) {
                $this->line("   ✅ API Call: Success");
                $this->line("   📝 Response: " . substr($response, 0, 200) . (strlen($response) > 200 ? '...' : ''));

                if ($debug) {
                    $this->line("   🔍 Full response length: " . strlen($response));
                    $this->newLine();
                    $this->line("   📋 Full Response:");
                    $this->line($response);
                }
            } else {
                $this->warn("   ⚠️  API Call returned empty response");
            }

        } catch (Exception $e) {
            $this->error("   ❌ API Call Failed: " . $e->getMessage());

            if ($debug) {
                $this->line("   🔍 Exception class: " . get_class($e));
                $this->line("   🔍 Exception code: " . $e->getCode());
                $this->newLine();
                $this->line("   📋 Full trace:");
                $this->line($e->getTraceAsString());
            }
        }
    }
}

<?php

namespace Modules\Page\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Role\Models\Permission;
use Modules\Role\Models\Role;

class PagePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedPagePermissions();
        $this->assignPermissionsToSuperAdmin();
    }

    /**
     * Seed page module permissions.
     */
    private function seedPagePermissions(): void
    {
        $permissions = [
            [
                'name' => 'page.view',
                'display_name' => 'View Pages',
                'description' => 'Permission to view pages list and details',
                'module_name' => 'page',
                'sort_order' => 1,
                'guard_name' => 'api',
            ],
            [
                'name' => 'page.create',
                'display_name' => 'Create Pages',
                'description' => 'Permission to create new pages',
                'module_name' => 'page',
                'sort_order' => 2,
                'guard_name' => 'api',
            ],
            [
                'name' => 'page.edit',
                'display_name' => 'Edit Pages',
                'description' => 'Permission to update existing pages',
                'module_name' => 'page',
                'sort_order' => 3,
                'guard_name' => 'api',
            ],
            [
                'name' => 'page.delete',
                'display_name' => 'Delete Pages',
                'description' => 'Permission to soft delete and restore pages',
                'module_name' => 'page',
                'sort_order' => 4,
                'guard_name' => 'api',
            ],
            [
                'name' => 'page.destroy',
                'display_name' => 'Destroy Pages',
                'description' => 'Permission to permanently delete pages',
                'module_name' => 'page',
                'sort_order' => 5,
                'guard_name' => 'api',
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => 'api'],
                array_merge($permission, ['guard_name' => 'api'])
            );
        }
    }

    /**
     * Assign page permissions to super-admin role.
     */
    private function assignPermissionsToSuperAdmin(): void
    {
        $superAdmin = Role::where('name', 'super-admin')->where('guard_name', 'api')->first();

        if ($superAdmin) {
            $pagePermissions = Permission::where('module_name', 'page')->where('guard_name', 'api')->get();
            
            // Gán tất cả quyền page cho super-admin
            foreach ($pagePermissions as $permission) {
                if (!$superAdmin->hasPermissionTo($permission->name)) {
                    $superAdmin->givePermissionTo($permission->name);
                }
            }
        }
    }
}

<?php

namespace Modules\Auth\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AuthNotificationFinalSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedFinalTemplates();
    }

    /**
     * Seed final Auth notification templates.
     */
    private function seedFinalTemplates(): void
    {
        // Get notification types
        $notificationTypes = DB::table('notification_types')
            ->whereIn('key', [
                'user.logged-in', 'password_reset', 'password_changed'
            ])
            ->get()
            ->keyBy('key');

        $templates = [];

        $this->command->info('Creating final Auth notification templates...');

        // User Logged In Templates
        if (isset($notificationTypes['user.logged-in'])) {
            $userLoggedInType = $notificationTypes['user.logged-in'];

            // English templates
            $templates[] = [
                'notification_type_id' => $userLoggedInType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => 'Login Successful',
                'content' => 'You have successfully logged in to {app_name} at {login_time}.',
                'variables' => json_encode(['user_name', 'app_name', 'login_time', 'ip_address', 'user_agent']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // Vietnamese templates
            $templates[] = [
                'notification_type_id' => $userLoggedInType->id,
                'channel' => 'database',
                'locale' => 'vi',
                'subject' => null,
                'title' => 'Đăng nhập thành công',
                'content' => 'Bạn đã đăng nhập thành công vào {app_name} lúc {login_time}.',
                'variables' => json_encode(['user_name', 'app_name', 'login_time', 'ip_address', 'user_agent']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Password Reset Templates
        if (isset($notificationTypes['password_reset'])) {
            $passwordResetType = $notificationTypes['password_reset'];

            // English templates
            $templates[] = [
                'notification_type_id' => $passwordResetType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => 'Password Reset Request',
                'content' => 'A password reset code has been sent to your email.',
                'variables' => json_encode(['user_name', 'app_name', 'reset_code', 'expires_in']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $passwordResetType->id,
                'channel' => 'email',
                'locale' => 'en',
                'subject' => 'Password Reset Code - {app_name}',
                'title' => 'Password Reset Request',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset - {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">🔒 Password Reset</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Hi <strong>{user_name}</strong>,</p>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            You have requested to reset your password for your {app_name} account.
        </p>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            Your password reset code is:
        </p>
        
        <div style="text-align: center; margin: 30px 0;">
            <div style="background: #dc3545; color: white; padding: 20px; border-radius: 10px; font-size: 32px; font-weight: bold; letter-spacing: 8px; display: inline-block; min-width: 200px;">
                {reset_code}
            </div>
        </div>
        
        <p style="font-size: 16px; margin-bottom: 20px; text-align: center;">
            <strong>This code will expire in {expires_in} minutes.</strong>
        </p>
        
        <p style="font-size: 14px; color: #666; margin-top: 20px;">
            If you didn\'t request this password reset, please ignore this email and your password will remain unchanged.
        </p>
        
        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">
        
        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Best regards,<br>
            The {app_name} Security Team
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'app_name', 'reset_code', 'expires_in']),
                'settings' => json_encode(['from_name' => '{app_name} Security', 'priority' => 'high']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // Vietnamese templates
            $templates[] = [
                'notification_type_id' => $passwordResetType->id,
                'channel' => 'database',
                'locale' => 'vi',
                'subject' => null,
                'title' => 'Yêu cầu đặt lại mật khẩu',
                'content' => 'Mã đặt lại mật khẩu đã được gửi đến email của bạn.',
                'variables' => json_encode(['user_name', 'app_name', 'reset_code', 'expires_in']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $passwordResetType->id,
                'channel' => 'email',
                'locale' => 'vi',
                'subject' => 'Mã đặt lại mật khẩu - {app_name}',
                'title' => 'Yêu cầu đặt lại mật khẩu',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Đặt lại mật khẩu - {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">🔒 Đặt lại mật khẩu</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Xin chào <strong>{user_name}</strong>,</p>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            Bạn đã yêu cầu đặt lại mật khẩu cho tài khoản {app_name} của mình.
        </p>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            Mã đặt lại mật khẩu của bạn là:
        </p>
        
        <div style="text-align: center; margin: 30px 0;">
            <div style="background: #dc3545; color: white; padding: 20px; border-radius: 10px; font-size: 32px; font-weight: bold; letter-spacing: 8px; display: inline-block; min-width: 200px;">
                {reset_code}
            </div>
        </div>
        
        <p style="font-size: 16px; margin-bottom: 20px; text-align: center;">
            <strong>Mã này sẽ hết hạn sau {expires_in} phút.</strong>
        </p>
        
        <p style="font-size: 14px; color: #666; margin-top: 20px;">
            Nếu bạn không yêu cầu đặt lại mật khẩu này, vui lòng bỏ qua email này và mật khẩu của bạn sẽ không thay đổi.
        </p>
        
        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">
        
        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Trân trọng,<br>
            Đội ngũ Bảo mật {app_name}
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'app_name', 'reset_code', 'expires_in']),
                'settings' => json_encode(['from_name' => 'Đội ngũ Bảo mật {app_name}', 'priority' => 'high']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Password Changed Templates
        if (isset($notificationTypes['password_changed'])) {
            $passwordChangedType = $notificationTypes['password_changed'];

            // English templates
            $templates[] = [
                'notification_type_id' => $passwordChangedType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => 'Password Changed Successfully',
                'content' => 'Your password has been changed successfully.',
                'variables' => json_encode(['user_name', 'app_name', 'change_time']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $passwordChangedType->id,
                'channel' => 'email',
                'locale' => 'en',
                'subject' => 'Password Changed - {app_name}',
                'title' => 'Password Changed Successfully',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Changed - {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">✅ Password Changed</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Hi <strong>{user_name}</strong>,</p>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            Your password for {app_name} has been changed successfully at {change_time}.
        </p>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            If you didn\'t make this change, please contact our support team immediately.
        </p>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="{support_url}" style="background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                Contact Support
            </a>
        </div>
        
        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">
        
        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Best regards,<br>
            The {app_name} Security Team
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'app_name', 'change_time', 'support_url']),
                'settings' => json_encode(['from_name' => '{app_name} Security', 'priority' => 'normal']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // Vietnamese templates
            $templates[] = [
                'notification_type_id' => $passwordChangedType->id,
                'channel' => 'database',
                'locale' => 'vi',
                'subject' => null,
                'title' => 'Đổi mật khẩu thành công',
                'content' => 'Mật khẩu của bạn đã được thay đổi thành công.',
                'variables' => json_encode(['user_name', 'app_name', 'change_time']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $passwordChangedType->id,
                'channel' => 'email',
                'locale' => 'vi',
                'subject' => 'Mật khẩu đã được thay đổi - {app_name}',
                'title' => 'Đổi mật khẩu thành công',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mật khẩu đã được thay đổi - {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">✅ Mật khẩu đã được thay đổi</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Xin chào <strong>{user_name}</strong>,</p>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            Mật khẩu của bạn cho tài khoản {app_name} đã được thay đổi thành công lúc {change_time}.
        </p>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            Nếu bạn không thực hiện thay đổi này, vui lòng liên hệ với đội ngũ hỗ trợ của chúng tôi ngay lập tức.
        </p>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="{support_url}" style="background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                Liên hệ hỗ trợ
            </a>
        </div>
        
        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">
        
        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Trân trọng,<br>
            Đội ngũ Bảo mật {app_name}
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'app_name', 'change_time', 'support_url']),
                'settings' => json_encode(['from_name' => 'Đội ngũ Bảo mật {app_name}', 'priority' => 'normal']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Insert all templates
        foreach ($templates as $template) {
            DB::table('notification_templates')->updateOrInsert(
                [
                    'notification_type_id' => $template['notification_type_id'],
                    'channel' => $template['channel'],
                    'locale' => $template['locale']
                ],
                $template
            );
        }

        $this->command->info('Final Auth notification templates seeded successfully. Total: ' . count($templates));
    }
}

<?php

namespace Modules\Auth\Http\Requests;

use Illuminate\Validation\Rules\Password;
use Modules\Core\Http\Requests\BaseFormRequest;
use Mo<PERSON>les\User\Enums\UserGender;
use Illuminate\Validation\Rule;

class RegisterRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $minLength = setting('security.password_min_length', 6);
        $requireNumbers = setting('security.password_require_numbers', false);
        $requireSymbols = setting('security.password_require_symbols', false);
        $requireUpperCase = setting('security.password_require_uppercase', false);
        $requireLowerCase = setting('security.password_require_lowercase', false);

        $passwordRule = Password::min($minLength);

        if ($requireNumbers) {
            $passwordRule->numbers();
        }

        if ($requireSymbols) {
            $passwordRule->symbols();
        }

        return [
            'username' => [
                'required',
                'string',
                'min:3',
                'max:50',
                'regex:/^[a-zA-Z][a-zA-Z0-9_-]*[a-zA-Z0-9]$/',
                Rule::unique('users')->whereNull('deleted_at'),
            ],
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->whereNull('deleted_at'),
            ],
            'password' => [
                'required',
                'confirmed',
                $passwordRule,
                'different:username',
                'different:email',
                $requireUpperCase ? $this->getUpperCaseRule() : null,
                $requireLowerCase ? $this->getLowerCaseRule() : null,
            ],
            'first_name' => [
                'required',
                'string',
                'max:100',
            ],
            'last_name' => [
                'required',
                'string',
                'max:100',
            ],
            'gender' => [
                'nullable',
                'string',
                Rule::in(UserGender::values()),
            ],
            'phone' => [
                'nullable',
                'string',
                'min:7',
                'max:15',
                'regex:/^(\+\d{1,3}|0)[0-9\s\-\(\)]{6,12}$/',
                Rule::unique('users')->whereNull('deleted_at'),
            ],
        ];
    }

    /**
     * Custom rule to check for uppercase letters
     */
    protected function getUpperCaseRule(): \Closure
    {
        return function ($attribute, $value, $fail) {
            if (!preg_match('/[A-Z]/', $value)) {
                $fail('The password must contain at least one uppercase letter.');
            }
        };
    }

    /**
     * Custom rule to check for lowercase letters
     */
    protected function getLowerCaseRule(): \Closure
    {
        return function ($attribute, $value, $fail) {
            if (!preg_match('/[a-z]/', $value)) {
                $fail('The password must contain at least one lowercase letter.');
            }
        };
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'username.required' => __('Username is required.'),
            'username.unique' => __('Username is already taken.'),
            'username.regex' => __('Username must start with a letter and contain only letters, numbers, hyphens, and underscores.'),
            'email.required' => __('Email is required.'),
            'email.email' => __('Please enter a valid email address.'),
            'email.unique' => __('Email is already registered.'),
            'password.required' => __('Password is required.'),
            'password.min' => __('Password must be at least 8 characters.'),
            'password.confirmed' => __('Password confirmation does not match.'),
            'first_name.required' => __('First name is required.'),
            'last_name.required' => __('Last name is required.'),
            'gender.required' => __('Gender is required.'),
            'gender.in' => __('Please select a valid gender.'),
            'phone.unique' => __('Phone number is already registered.'),
            'phone.regex' => __('Please enter a valid phone number.'),
        ];
    }
}

<?php

namespace Modules\Auth\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Modules\Auth\Models\AuthLoginAttempt;

/**
 * AuthRateLimitService
 * 
 * Handles rate limiting for authentication endpoints using cache-based tracking.
 * Settings are retrieved from the Setting module for flexible configuration.
 */
class AuthRateLimitService
{
    protected const CACHE_PREFIX = 'auth_rate_limit_';
    protected const LOCKOUT_PREFIX = 'auth_lockout_';

    /**
     * Check if request is rate limited for specific action.
     */
    public function isRateLimited(Request $request, string $action): bool
    {
        $identifier = $this->getIdentifier($request, $action);
        $key = $this->getCacheKey($action, $identifier);
        
        $maxAttempts = $this->getMaxAttempts($action);
        $windowMinutes = setting_int('auth.auth_rate_limit_window_minutes', 60);
        
        $attempts = Cache::get($key, 0);
        
        return $attempts >= $maxAttempts;
    }

    /**
     * Check if account is locked out.
     */
    public function isLockedOut(string $identifier): bool
    {
        $lockoutKey = self::LOCKOUT_PREFIX . $identifier;
        return Cache::has($lockoutKey);
    }

    /**
     * Record an attempt for rate limiting.
     */
    public function recordAttempt(Request $request, string $action, bool $successful = false, string $failureReason = null): void
    {
        $identifier = $this->getIdentifier($request, $action);
        $key = $this->getCacheKey($action, $identifier);
        $windowMinutes = setting_int('auth.auth_rate_limit_window_minutes', 60);

        // Increment attempt counter
        $attempts = Cache::get($key, 0) + 1;
        Cache::put($key, $attempts, now()->addMinutes($windowMinutes));

        // Store attempt in database for tracking
        AuthLoginAttempt::create([
            'identifier' => $identifier,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'action' => $action,
            'successful' => $successful,
            'failure_reason' => $failureReason,
            'attempted_at' => now(),
            'additional_data' => [
                'attempts_count' => $attempts,
                'max_attempts' => $this->getMaxAttempts($action),
            ]
        ]);

        // For login attempts, check if we need to lock the account
        if ($action === 'login' && !$successful) {
            $this->handleFailedLogin($identifier, $attempts);
        }

        // Clear rate limit on successful attempt for certain actions
        if ($successful && in_array($action, ['login', 'register'])) {
            $this->clearRateLimit($request, $action);
        }

        // Log suspicious activity
        $maxAttempts = $this->getMaxAttempts($action);
        if ($attempts > ($maxAttempts * 0.8)) {
            Log::warning('High rate limit activity detected', [
                'action' => $action,
                'identifier' => $identifier,
                'attempts' => $attempts,
                'max_attempts' => $maxAttempts,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);
        }
    }

    /**
     * Clear rate limit for successful attempts.
     */
    public function clearRateLimit(Request $request, string $action): void
    {
        $identifier = $this->getIdentifier($request, $action);
        $key = $this->getCacheKey($action, $identifier);
        Cache::forget($key);
    }

    /**
     * Handle failed login attempts and potential lockout.
     */
    protected function handleFailedLogin(string $identifier, int $attempts): void
    {
        $maxAttempts = setting_int('auth.auth_login_attempts_limit', 5);
        $lockoutDuration = setting_int('auth.auth_lockout_duration', 60);
        
        if ($attempts >= $maxAttempts) {
            $lockoutKey = self::LOCKOUT_PREFIX . $identifier;
            Cache::put($lockoutKey, true, now()->addMinutes($lockoutDuration));
            
            Log::warning('Account locked due to failed login attempts', [
                'identifier' => $identifier,
                'attempts' => $attempts,
                'lockout_duration' => $lockoutDuration
            ]);
        }
    }

    /**
     * Get rate limit identifier based on action and request.
     */
    public function getIdentifier(Request $request, string $action): string
    {
        switch ($action) {
            case 'login':
                return $request->input('login', $request->ip());
            
            case 'register':
                return $request->input('email', $request->ip());
            
            case 'password_reset':
            case 'email_verification':
                return $request->input('email', $request->ip());
            
            case 'oauth':
                return $request->ip();
            
            default:
                return $request->ip();
        }
    }

    /**
     * Get cache key for rate limiting.
     */
    protected function getCacheKey(string $action, string $identifier): string
    {
        return self::CACHE_PREFIX . $action . '_' . hash('sha256', $identifier);
    }

    /**
     * Get maximum attempts for specific action.
     */
    public function getMaxAttempts(string $action): int
    {
        $settingMap = [
            'login' => 'auth.auth_login_attempts_limit',
            'register' => 'auth.auth_rate_limit_register_attempts',
            'password_reset' => 'auth.auth_rate_limit_password_reset_attempts',
            'email_verification' => 'auth.auth_rate_limit_email_verification_attempts',
            'oauth' => 'auth.auth_rate_limit_oauth_attempts',
        ];

        $settingKey = $settingMap[$action] ?? 'auth.auth_login_attempts_limit';
        $defaultValue = match($action) {
            'login' => 5,
            'register' => 3,
            'password_reset' => 3,
            'email_verification' => 5,
            'oauth' => 10,
            default => 5
        };

        return setting_int($settingKey, $defaultValue);
    }

    /**
     * Get remaining attempts for an identifier and action.
     */
    public function getRemainingAttempts(Request $request, string $action): int
    {
        $identifier = $this->getIdentifier($request, $action);
        $key = $this->getCacheKey($action, $identifier);
        
        $maxAttempts = $this->getMaxAttempts($action);
        $currentAttempts = Cache::get($key, 0);
        
        return max(0, $maxAttempts - $currentAttempts);
    }

    /**
     * Get time until rate limit resets.
     */
    public function getResetTime(Request $request, string $action): ?int
    {
        $identifier = $this->getIdentifier($request, $action);
        $key = $this->getCacheKey($action, $identifier);
        
        if (!Cache::has($key)) {
            return null;
        }
        
        // Get TTL in seconds
        $store = Cache::getStore();

        // Handle different cache drivers
        if (method_exists($store, 'getRedis')) {
            return $store->getRedis()->ttl($store->getPrefix() . $key);
        }

        // Fallback for non-Redis drivers (like ArrayStore in testing)
        return 3600; // Default 1 hour
    }

    /**
     * Get lockout time remaining in seconds.
     */
    public function getLockoutTimeRemaining(string $identifier): ?int
    {
        $lockoutKey = self::LOCKOUT_PREFIX . $identifier;
        
        if (!Cache::has($lockoutKey)) {
            return null;
        }
        
        // Get TTL in seconds
        $store = Cache::getStore();

        // Handle different cache drivers
        if (method_exists($store, 'getRedis')) {
            return $store->getRedis()->ttl($store->getPrefix() . $lockoutKey);
        }

        // Fallback for non-Redis drivers (like ArrayStore in testing)
        return 3600; // Default 1 hour
    }

    /**
     * Clear lockout for identifier.
     */
    public function clearLockout(string $identifier): void
    {
        $lockoutKey = self::LOCKOUT_PREFIX . $identifier;
        Cache::forget($lockoutKey);
        
        Log::info('Account lockout cleared', [
            'identifier' => $identifier
        ]);
    }

    /**
     * Get rate limit status for debugging.
     */
    public function getStatus(Request $request, string $action): array
    {
        $identifier = $this->getIdentifier($request, $action);
        $key = $this->getCacheKey($action, $identifier);
        
        return [
            'action' => $action,
            'identifier' => $identifier,
            'current_attempts' => Cache::get($key, 0),
            'max_attempts' => $this->getMaxAttempts($action),
            'remaining_attempts' => $this->getRemainingAttempts($request, $action),
            'is_rate_limited' => $this->isRateLimited($request, $action),
            'is_locked_out' => $this->isLockedOut($identifier),
            'reset_time' => $this->getResetTime($request, $action),
            'lockout_time_remaining' => $this->getLockoutTimeRemaining($identifier),
        ];
    }
}

<?php

use Illuminate\Support\Facades\Route;
use <PERSON><PERSON><PERSON>\Role\Http\Controllers\Auth\RoleController as AuthRoleController;
use Mo<PERSON>les\Role\Http\Controllers\Auth\PermissionController;

// Authenticated API routes (admin/auth)
Route::middleware(['auth:api'])->prefix('v1/auth')->group(function () {
    // Roles management
    Route::get('roles/dropdown', [AuthRoleController::class, 'dropdown'])->name('auth.role.dropdown');

    // Standard CRUD routes
    Route::get('roles', [AuthRoleController::class, 'index'])->name('auth.role.index');
    Route::post('roles', [AuthRoleController::class, 'store'])->name('auth.role.store');
    Route::get('roles/{id}', [AuthRoleController::class, 'show'])->name('auth.role.show');

    // Bulk operations
    Route::delete('roles/bulk/delete', [AuthRoleController::class, 'bulkDelete'])->name('auth.role.bulk-delete');
    Route::delete('roles/bulk/force', [AuthRoleController::class, 'bulkDestroy'])->name('auth.role.bulk-destroy');
    Route::put('roles/bulk/restore', [AuthRoleController::class, 'bulkRestore'])->name('auth.role.bulk-restore');


    // Custom delete operations
    Route::delete('roles/{id}/delete', [AuthRoleController::class, 'delete'])->name('auth.role.delete');
    Route::delete('roles/{id}/force', [AuthRoleController::class, 'destroy'])->name('auth.role.destroy');

    // Restore operations
    Route::put('roles/{id}/restore', [AuthRoleController::class, 'restore'])->name('auth.role.restore');
    Route::put('roles/{id}', [AuthRoleController::class, 'update'])->name('auth.role.update');

    Route::get('permissions', [PermissionController::class, 'index'])->name('auth.permission.index');

});

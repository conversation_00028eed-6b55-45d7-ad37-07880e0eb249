<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\ActiveCode\Database\Seeders\ActiveCodeDatabaseSeeder;
use Modules\Auth\Database\Seeders\AuthDatabaseSeeder;
use Modules\Billing\Database\Seeders\BillingDatabaseSeeder;
use Modules\Blog\Database\Seeders\BlogDatabaseSeeder;
use Modules\ChatBot\Database\Seeders\ChatBotDatabaseSeeder;
use Modules\Currency\Database\Seeders\CurrencyDatabaseSeeder;
use Modules\Language\Database\Seeders\LanguageDatabaseSeeder;
use Modules\Location\Database\Seeders\LocationDatabaseSeeder;
use Modules\ModelAI\Database\Seeders\ModelAIDatabaseSeeder;
use Modules\Notification\Database\Seeders\NotificationDatabaseSeeder;
use Modules\Organization\Database\Seeders\OrganizationDatabaseSeeder;
use Modules\Page\Database\Seeders\PageDatabaseSeeder;
use Modules\Payment\Database\Seeders\PaymentDatabaseSeeder;
use Mo<PERSON>les\Role\Database\Seeders\RoleDatabaseSeeder;
use Mo<PERSON>les\Setting\Database\Seeders\SettingDatabaseSeeder;
use Modules\Theme\Database\Seeders\ThemeDatabaseSeeder;
use Modules\Translation\Database\Seeders\TranslationDatabaseSeeder;
use Modules\User\Database\Seeders\UserDatabaseSeeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            // Core modules first
            LanguageDatabaseSeeder::class,
            SettingDatabaseSeeder::class,
            NotificationDatabaseSeeder::class,
            AuthDatabaseSeeder::class,
            ActiveCodeDatabaseSeeder::class,

            // Role and User modules
            RoleDatabaseSeeder::class,
            UserDatabaseSeeder::class,

            // Content modules
            PageDatabaseSeeder::class,
            BlogDatabaseSeeder::class,
            TranslationDatabaseSeeder::class,

            // System modules
            CurrencyDatabaseSeeder::class,
            ThemeDatabaseSeeder::class,
            LocationDatabaseSeeder::class,

            // Business modules
            OrganizationDatabaseSeeder::class,
            PaymentDatabaseSeeder::class,
            BillingDatabaseSeeder::class,

            // AI modules (depends on User)
            ModelAIDatabaseSeeder::class,
            ChatBotDatabaseSeeder::class,
        ]);
    }
}

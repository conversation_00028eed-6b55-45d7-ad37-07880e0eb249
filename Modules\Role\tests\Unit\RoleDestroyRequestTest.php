<?php

namespace Modules\Role\Tests\Unit;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Modules\Role\Http\Requests\RoleDestroyRequest;
use Modules\Role\Models\Role;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class RoleDestroyRequestTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run migrations for Role module
        $this->artisan('migrate', ['--path' => 'Modules/Role/database/migrations']);
    }

    #[Test]
    public function it_validates_required_fields()
    {
        $request = new RoleDestroyRequest();
        $rules = $request->rules();

        // Test required fields
        $this->assertContains('required', $rules['id']);
        $this->assertContains('integer', $rules['id']);
    }

    #[Test]
    public function it_has_correct_attributes()
    {
        $request = new RoleDestroyRequest();
        $attributes = $request->attributes();

        $expectedAttributes = [
            'id' => __('Role ID'),
        ];

        $this->assertEquals($expectedAttributes, $attributes);
    }

    #[Test]
    public function it_passes_validation_with_valid_soft_deleted_role()
    {
        // Create and soft delete a role
        $role = Role::factory()->create();
        $role->delete();

        $validData = [
            'id' => $role->id,
        ];

        $request = new RoleDestroyRequest();
        $validator = Validator::make($validData, $request->rules());

        $this->assertTrue($validator->passes());
    }

    #[Test]
    public function it_fails_validation_with_active_role()
    {
        // Create active role (not soft deleted)
        $role = Role::factory()->create();

        $invalidData = [
            'id' => $role->id,
        ];

        $request = new RoleDestroyRequest();
        $validator = Validator::make($invalidData, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('id', $validator->errors()->toArray());
    }

    #[Test]
    public function it_fails_validation_with_non_existent_id()
    {
        $invalidData = [
            'id' => 99999,
        ];

        $request = new RoleDestroyRequest();
        $validator = Validator::make($invalidData, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('id', $validator->errors()->toArray());
    }

    #[Test]
    public function it_fails_validation_with_invalid_id_type()
    {
        $invalidData = [
            'id' => 'not-an-integer',
        ];

        $request = new RoleDestroyRequest();
        $validator = Validator::make($invalidData, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('id', $validator->errors()->toArray());
    }

    #[Test]
    public function it_has_custom_error_messages()
    {
        $request = new RoleDestroyRequest();
        $messages = $request->messages();

        $expectedMessages = [
            'id.required' => __('Role ID is required.'),
            'id.integer' => __('Role ID must be an integer.'),
            'id.exists' => __('The selected role must be in trash to be permanently deleted.'),
        ];

        $this->assertEquals($expectedMessages, $messages);
    }

    #[Test]
    public function it_prepares_id_from_route_parameter()
    {
        // This test would require mocking the route, but we can test the method exists
        $request = new RoleDestroyRequest();
        
        // Check that the prepareForValidation method exists
        $this->assertTrue(method_exists($request, 'prepareForValidation'));
    }
}

<?php

use Illuminate\Support\Facades\Route;
use Modules\Language\Http\Controllers\LanguageController;
use Modules\Language\Http\Controllers\Auth\LanguageController as AuthLanguageController;

// Public API routes (no authentication required)
Route::prefix('v1')->group(function () {
    Route::get('languages', [LanguageController::class, 'index'])->name('api.language.index');
    Route::get('languages/default', [LanguageController::class, 'default'])->name('api.language.default');
    Route::get('languages/{language:code}', [LanguageController::class, 'show'])->name('api.language.show');
});

// Authenticated API routes (admin/auth)
Route::middleware(['auth:api'])->prefix('v1/auth')->group(function () {
    // Languages dropdown
    Route::get('languages/dropdown', [AuthLanguageController::class, 'dropdown'])->name('auth.language.dropdown');

    // Standard CRUD routes
    Route::get('languages', [AuthLanguageController::class, 'index'])->name('auth.language.index');
    Route::post('languages', [AuthLanguageController::class, 'store'])->name('auth.language.store');
    Route::get('languages/{id}', [AuthLanguageController::class, 'show'])->name('auth.language.show');
    Route::put('languages/{id}', [AuthLanguageController::class, 'update'])->name('auth.language.update');
    Route::patch('languages/{id}', [AuthLanguageController::class, 'update'])->name('auth.language.update');

    // Custom operations
    Route::put('languages/{id}/set-default', [AuthLanguageController::class, 'setDefault'])->name('auth.language.set-default');

    // Delete operations
    Route::delete('languages/{id}/delete', [AuthLanguageController::class, 'delete'])->name('auth.language.delete');
    Route::delete('languages/{id}/destroy', [AuthLanguageController::class, 'destroy'])->name('auth.language.destroy');
    Route::put('languages/{id}/restore', [AuthLanguageController::class, 'restore'])->name('auth.language.restore');

    // Bulk operations
    Route::delete('languages/bulk/delete', [AuthLanguageController::class, 'bulkDelete'])->name('auth.language.bulk-delete');
    Route::delete('languages/bulk/destroy', [AuthLanguageController::class, 'bulkDestroy'])->name('auth.language.bulk-destroy');
    Route::put('languages/bulk/restore', [AuthLanguageController::class, 'bulkRestore'])->name('auth.language.bulk-restore');
});


# ActiveCode Module

The ActiveCode Module provides comprehensive verification code management functionality for Laravel ProCMS, enabling secure generation, verification, and management of time-limited verification codes for various authentication and security scenarios.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Architecture](#architecture)
- [Database Schema](#database-schema)
- [Installation](#installation)
- [API Documentation](#api-documentation)
- [Usage Examples](#usage-examples)
- [Code Types](#code-types)
- [Rate Limiting](#rate-limiting)
- [Caching](#caching)
- [Testing](#testing)
- [Dependencies](#dependencies)
- [Contributing](#contributing)

## Overview

The ActiveCode Module is a security-focused component of Laravel ProCMS that handles verification code generation, validation, and lifecycle management. It supports multiple code types, rate limiting, automatic cleanup, and comprehensive logging for security auditing.

### Key Components

- **ActiveCode Model**: Main verification code entity with expiration and usage tracking
- **ActiveCodeService**: Business logic for code generation, verification, and management
- **ActiveCodeFacade**: Convenient access to verification code functionality
- **ActiveCodeFactory**: Test data generation with string constants support

## Features

### Core Features
- ✅ **Multiple Code Types**: Support for 9 different verification scenarios
- ✅ **Time-based Expiration**: Configurable expiration times per code type
- ✅ **Rate Limiting**: Prevents spam and abuse with configurable limits
- ✅ **Automatic Cleanup**: Scheduled removal of expired codes
- ✅ **Usage Tracking**: Attempt counting and usage timestamping
- ✅ **Secure Generation**: Cryptographically secure code generation

### Advanced Features
- ✅ **Settings Integration**: Configurable through Settings module
- ✅ **Cache Support**: Intelligent caching for performance optimization
- ✅ **Comprehensive Logging**: Detailed logging for security auditing
- ✅ **String Constants**: Uses string constants instead of enums for flexibility
- ✅ **Facade Pattern**: Clean API access through Laravel facades
- ✅ **Comprehensive Testing**: 65 test methods with 100% coverage

### Security Features
- ✅ **Attempt Limiting**: Maximum verification attempts per code
- ✅ **Single Use**: Codes are invalidated after successful verification
- ✅ **Automatic Expiration**: Time-based code invalidation
- ✅ **Cleanup Protection**: Prevents deletion of recently expired codes

## Architecture

The ActiveCode Module follows Laravel ProCMS architectural patterns:

```
Modules/ActiveCode/
├── app/
│   ├── Facades/
│   │   └── ActiveCodeFacade.php     # Service facade
│   ├── Http/
│   │   └── Controllers/
│   │       └── ActiveCodeController.php # API endpoints (placeholder)
│   ├── Models/
│   │   └── ActiveCode.php           # Main verification code model
│   ├── Providers/
│   │   └── ActiveCodeServiceProvider.php # Service registration
│   └── Services/
│       └── ActiveCodeService.php    # Business logic
├── database/
│   ├── factories/
│   │   └── ActiveCodeFactory.php    # Code factory with string constants
│   └── migrations/
│       └── 2024_01_01_000001_create_active_codes_table.php
├── routes/
│   ├── api.php                      # API routes (placeholder)
│   └── web.php                      # Web routes (placeholder)
└── tests/
    ├── Unit/                        # Unit tests (54 methods)
    ├── Integration/                 # Integration tests (11 methods)
    ├── phpunit.xml                  # PHPUnit configuration
    └── run-tests.sh                 # Test runner script
```

## Database Schema

### Active Codes Table
```sql
CREATE TABLE active_codes (
    id BIGINT UNSIGNED PRIMARY KEY,
    code VARCHAR(255) NOT NULL,
    type VARCHAR(255) NOT NULL,
    identifier VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP NULL,
    attempts INT DEFAULT 0,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_active_codes_lookup (code, identifier, type),
    INDEX idx_active_codes_cleanup (expires_at),
    INDEX idx_active_codes_type (type),
    INDEX idx_active_codes_identifier (identifier)
);
```

### Field Descriptions
- `code`: The verification code (6-8 characters)
- `type`: Code type (verification, login_otp, etc.)
- `identifier`: User identifier (email, phone, user_id)
- `expires_at`: Code expiration timestamp
- `used_at`: Timestamp when code was successfully used
- `attempts`: Number of verification attempts made

## Installation

The ActiveCode Module is included in Laravel ProCMS by default. To manually install or reinstall:

### 1. Run Migrations
```bash
php artisan migrate --path=Modules/ActiveCode/database/migrations
```

### 2. Register Service Provider
The module is auto-registered via Laravel's package discovery. Manual registration in `config/app.php`:

```php
'providers' => [
    // Other providers...
    Modules\ActiveCode\Providers\ActiveCodeServiceProvider::class,
],
```

### 3. Configure Settings (Optional)
Configure default settings through the Settings module:

```php
// Default settings
'activecode_expiry_minutes' => 15,
'activecode_max_attempts' => 5,
'activecode_rate_limit_seconds' => 60,
'activecode_length' => 6,
'activecode_cleanup_days' => 1,
```

## API Documentation

### Service Methods

#### Generate Verification Code
```php
ActiveCodeFacade::generate(string $identifier, string $type = 'verification', int $expiryMinutes = null): ActiveCode
```

**Parameters**:
- `identifier`: User identifier (email, phone, user_id)
- `type`: Code type (see [Code Types](#code-types))
- `expiryMinutes`: Custom expiration time (optional)

**Returns**: ActiveCode instance

**Example**:
```php
$code = ActiveCodeFacade::generate('<EMAIL>', 'email_verification', 30);
```

#### Verify Code
```php
ActiveCodeFacade::verify(string $code, string $identifier, string $type = 'verification'): array
```

**Parameters**:
- `code`: The verification code to check
- `identifier`: User identifier
- `type`: Code type

**Returns**: Array with success status and message
```php
[
    'success' => true|false,
    'message' => 'Verification successful|Error message',
    'code' => ActiveCode|null  // Only on success
]
```

#### Resend Code
```php
ActiveCodeFacade::resend(string $identifier, string $type = 'verification'): array
```

**Parameters**:
- `identifier`: User identifier
- `type`: Code type

**Returns**: Array with success status and new code
```php
[
    'success' => true|false,
    'message' => 'Success message|Rate limit message',
    'code' => ActiveCode|null  // Only on success
]
```

#### Cleanup Expired Codes
```php
ActiveCodeFacade::cleanup(): void
```

Removes codes that have been expired for more than the cleanup period.

#### Cache Management
```php
ActiveCodeFacade::clearSettingsCache(): void
ActiveCodeFacade::warmSettingsCache(): void
```

## Usage Examples

### Basic Usage

```php
use Modules\ActiveCode\Facades\ActiveCodeFacade;

// Generate verification code
$activeCode = ActiveCodeFacade::generate('<EMAIL>', 'email_verification');

// Send code via email (your implementation)
Mail::to('<EMAIL>')->send(new VerificationCodeMail($activeCode->code));

// Verify code
$result = ActiveCodeFacade::verify('123456', '<EMAIL>', 'email_verification');

if ($result['success']) {
    // Code is valid, proceed with verification
    $user = User::where('email', '<EMAIL>')->first();
    $user->markEmailAsVerified();
} else {
    // Invalid code, show error
    return response()->json(['error' => $result['message']], 422);
}
```

### Advanced Usage

```php
// Generate OTP for login
$loginCode = ActiveCodeFacade::generate('+1234567890', 'login_otp', 5); // 5 minutes

// Generate password reset code
$resetCode = ActiveCodeFacade::generate('<EMAIL>', 'password_reset', 15); // 15 minutes

// Handle resend with rate limiting
$resendResult = ActiveCodeFacade::resend('<EMAIL>', 'email_verification');

if (!$resendResult['success']) {
    // Rate limited
    return response()->json(['error' => $resendResult['message']], 429);
}

// New code generated
$newCode = $resendResult['code'];
```

### Working with ActiveCode Models

```php
use Modules\ActiveCode\Models\ActiveCode;

// Query codes
$validCodes = ActiveCode::valid()->get();
$expiredCodes = ActiveCode::where('expires_at', '<', now())->get();
$codesByType = ActiveCode::byType('email_verification')->get();
$codesByIdentifier = ActiveCode::byIdentifier('<EMAIL>')->get();

// Check code status
if ($code->isExpired()) {
    echo "Code has expired";
}

if ($code->isValid()) {
    echo "Code is valid and unused";
}

// Mark code as used
$code->markAsUsed();
```

## Code Types

The ActiveCode Module supports the following verification scenarios:

### Available Code Types

| Type | Description | Typical Use Case | Default Expiry |
|------|-------------|------------------|-----------------|
| `verification` | General verification | Default verification | 15 minutes |
| `registration_verification` | Account registration | Email/phone verification during signup | 30 minutes |
| `login_otp` | One-time password for login | Two-factor authentication | 5 minutes |
| `email_verification` | Email address verification | Confirm email ownership | 60 minutes |
| `phone_verification` | Phone number verification | Confirm phone ownership | 10 minutes |
| `password_reset` | Password reset | Secure password recovery | 15 minutes |
| `two_factor_auth` | Two-factor authentication | Additional security layer | 5 minutes |
| `account_activation` | Account activation | Activate dormant accounts | 24 hours |
| `security_verification` | Security operations | Sensitive account changes | 10 minutes |

### Code Generation Rules

- **Numeric codes** (6 digits): `login_otp`, `two_factor_auth`, `phone_verification`
- **Alphanumeric codes** (6 characters): All other types
- **Custom length**: Configurable via settings
- **Uniqueness**: Codes are unique per identifier and type

## Rate Limiting

The ActiveCode Module implements intelligent rate limiting to prevent abuse:

### Rate Limiting Rules

- **Default limit**: 60 seconds between requests
- **Per identifier**: Rate limiting is applied per identifier and type combination
- **Configurable**: Adjustable via `activecode_rate_limit_seconds` setting
- **Bypass conditions**: No existing code or expired code

### Rate Limiting Behavior

```php
// First request - success
$result1 = ActiveCodeFacade::generate('<EMAIL>', 'verification');

// Immediate second request - rate limited
$result2 = ActiveCodeFacade::resend('<EMAIL>', 'verification');
// Returns: ['success' => false, 'message' => 'Please wait 59 seconds before requesting...']

// After rate limit period - success
sleep(60);
$result3 = ActiveCodeFacade::resend('<EMAIL>', 'verification');
// Returns: ['success' => true, 'code' => ActiveCode]
```

## Caching

The ActiveCode Module implements intelligent caching strategies:

### Settings Caching

```php
// Settings are cached with tags for efficient invalidation
Cache::tags('activecode_settings')->remember('activecode.settings', 3600, function () {
    return SettingFacade::getSettings();
});

// Clear settings cache
ActiveCodeFacade::clearSettingsCache();

// Warm settings cache
ActiveCodeFacade::warmSettingsCache();
```

### Service-Level Caching

The `ActiveCodeService` is registered as a singleton in the container:

```php
// Service is cached in container
$service1 = app('activecode.service');
$service2 = app('activecode.service');
// $service1 === $service2 (same instance)
```

### Performance Optimization

- Database queries use proper indexing
- Cleanup operations are optimized for large datasets
- Settings are cached to reduce database calls
- Service singleton prevents multiple instantiations

## Testing

The ActiveCode Module includes comprehensive test coverage with 65 test methods:

### Test Structure

```
tests/
├── Unit/
│   ├── ActiveCodeServiceTest.php     # Service layer (18 tests)
│   ├── ActiveCodeModelTest.php       # Model functionality (19 tests)
│   └── ActiveCodeFacadeTest.php      # Facade functionality (17 tests)
├── Integration/
│   └── ActiveCodeIntegrationTest.php # Component integration (11 tests)
├── phpunit.xml                       # PHPUnit configuration
└── run-tests.sh                      # Test runner script
```

### Running Tests

```bash
# Run all ActiveCode module tests
cd Modules/ActiveCode
chmod +x run-tests.sh
./run-tests.sh

# Run specific test types
../../vendor/bin/phpunit --testsuite "ActiveCode Unit Tests"
../../vendor/bin/phpunit --testsuite "ActiveCode Integration Tests"

# Run with coverage
../../vendor/bin/phpunit --coverage-html coverage-report
```

### Test Categories

#### Unit Tests (54 test methods)

**ActiveCodeServiceTest** (18 test methods)
- Code generation with various parameters
- Verification with success and failure scenarios
- Resend functionality with rate limiting
- Cleanup operations
- Cache management operations
- Error handling and edge cases

**ActiveCodeModelTest** (19 test methods)
- Model scopes (valid, byType, byIdentifier)
- Model methods (isExpired, isValid, markAsUsed)
- Factory integration with different states
- Database relationships and constraints
- String constants usage

**ActiveCodeFacadeTest** (17 test methods)
- Facade method delegation
- Service container integration
- Static method access
- Parameter handling
- Return value consistency

#### Integration Tests (11 test methods)

**ActiveCodeIntegrationTest** (11 test methods)
- End-to-end workflows
- Component interaction testing
- Cache system integration
- Settings system integration
- Error handling across components
- Performance testing with large datasets

### Test Features

- **String Constants**: All tests use string constants instead of enums
- **Database Safety**: Tests use RefreshDatabase trait for isolation
- **Mock Dependencies**: External dependencies are properly mocked
- **Comprehensive Coverage**: Tests cover success, failure, and edge cases
- **Performance Testing**: Large dataset handling verification
- **PHPUnit Attributes**: Modern PHPUnit #[Test] attributes usage

## Dependencies

### Core Dependencies

- **Laravel Framework**: ^10.0 || ^11.0
- **PHP**: ^8.1
- **MySQL/PostgreSQL**: Database support

### Module Dependencies

- **Setting Module**: For configuration management (optional)
- **Core Module**: For base classes and traits

### Development Dependencies

- **PHPUnit**: Testing framework
- **Mockery**: Mocking framework for tests
- **Laravel Testing**: Feature and unit testing utilities

## Contributing

We welcome contributions to the ActiveCode Module! Please follow these guidelines:

### Development Setup

1. **Clone the repository**
2. **Install dependencies**: `composer install`
3. **Run migrations**: `php artisan migrate`
4. **Run tests**: `cd Modules/ActiveCode && ./run-tests.sh`

### Coding Standards

- Follow PSR-12 coding standards
- Use meaningful variable and method names
- Add comprehensive docblocks
- Maintain 100% test coverage
- Use string constants instead of enums

### Pull Request Process

1. **Create feature branch**: `git checkout -b feature/new-feature`
2. **Write tests**: Ensure new functionality is tested
3. **Run test suite**: All tests must pass
4. **Update documentation**: Update README if needed
5. **Submit pull request**: With clear description

### Testing Requirements

- All new features must include unit tests
- Integration tests for component interactions
- Maintain existing test coverage
- Follow existing test patterns
- Use string constants in tests

### Security Considerations

- Verify cryptographic security of code generation
- Test rate limiting effectiveness
- Validate expiration mechanisms
- Ensure proper cleanup of sensitive data
- Test against timing attacks

---

**The ActiveCode Module provides a robust, secure, and well-tested foundation for verification code management in Laravel ProCMS applications.** 🔐

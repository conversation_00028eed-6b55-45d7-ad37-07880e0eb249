<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\Organization\Models\Organization;
use Modules\User\Models\User;

class DebugOrganizationDelete extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'debug:organization-delete {user_id} {organization_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Debug organization deletion permissions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id');
        $organizationId = $this->argument('organization_id');
        
        $user = User::find($userId);
        $organization = Organization::find($organizationId);
        
        if (!$user || !$organization) {
            $this->error("User or organization not found");
            return;
        }

        $this->info("=== DEBUG ORGANIZATION DELETE ===");
        $this->info("User: {$user->email} (ID: {$user->id})");
        $this->info("Organization: {$organization->name} (ID: {$organization->id})");
        $this->info("Organization Owner: {$organization->owner_id}");
        $this->line("");
        
        // Check user roles
        $this->info("User Roles:");
        $roles = $user->roles()->where('guard_name', 'api')->pluck('name')->toArray();
        if (empty($roles)) {
            $this->line("- No roles");
        } else {
            foreach ($roles as $role) {
                $this->line("- {$role}");
            }
        }
        $this->line("");
        
        // Check permissions
        $this->info("User Permissions:");
        $hasManage = $user->hasPermissionTo('organization.manage', 'api');
        $hasDestroy = $user->hasPermissionTo('organization.destroy', 'api');
        $this->line("- organization.manage: " . ($hasManage ? 'YES' : 'NO'));
        $this->line("- organization.destroy: " . ($hasDestroy ? 'YES' : 'NO'));
        $this->line("");
        
        // Check policy before method
        $this->info("Policy Checks:");
        $policy = new \Modules\Organization\Policies\OrganizationPolicy();
        $beforeResult = $policy->before($user, 'delete');
        $this->line("- before() result: " . ($beforeResult === true ? 'TRUE' : ($beforeResult === false ? 'FALSE' : 'NULL')));
        
        $deleteResult = $policy->delete($user, $organization);
        $this->line("- delete() result: " . ($deleteResult ? 'TRUE' : 'FALSE'));
        
        $canDelete = $user->can('delete', $organization);
        $this->line("- user->can('delete', org): " . ($canDelete ? 'TRUE' : 'FALSE'));
        $this->line("");
        
        // Check ownership
        $this->info("Ownership Check:");
        $isOwner = $user->id === $organization->owner_id;
        $this->line("- Is owner: " . ($isOwner ? 'YES' : 'NO'));
        $this->line("- User ID: {$user->id}");
        $this->line("- Owner ID: {$organization->owner_id}");
        $this->line("- IDs match: " . ($user->id == $organization->owner_id ? 'YES' : 'NO'));
        $this->line("- Strict match: " . ($user->id === $organization->owner_id ? 'YES' : 'NO'));
        
        // Final recommendation
        $this->line("");
        if ($canDelete) {
            $this->info("✓ User SHOULD be able to delete this organization");
        } else {
            $this->error("✗ User CANNOT delete this organization");
            if (!$isOwner) {
                $this->error("Reason: User is not the owner");
            }
        }
    }
}

<?php

namespace Modules\ChatBot\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;

class KnowledgeBaseFileUploadRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'nullable|string|max:255',
            'file' => 'required|file|mimes:txt,md,doc,docx,xls,xlsx,ppt,pptx,pdf,png,jpg,jpeg|max:102400', // 100MB
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'file.required' => 'File is required for upload.',
            'file.file' => 'The uploaded item must be a valid file.',
            'file.mimes' => 'File must be one of the following types: txt, md, doc, docx, xls, xlsx, ppt, pptx, pdf, png, jpg, jpeg.',
            'file.max' => 'File size cannot exceed 100MB.',
            'name.string' => 'Name must be a string.',
            'name.max' => 'Name cannot exceed 255 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'file' => 'file',
            'name' => 'name',
        ];
    }
}

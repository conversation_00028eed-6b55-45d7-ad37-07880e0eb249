<?php

namespace Modules\Role\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Modules\Role\Enums\RoleStatus;

/**
 * @property mixed $id
 * @property mixed $role_id
 */
class RoleRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
                'alpha_dash',
                Rule::unique('roles')->ignore($this->id)->whereNull('deleted_at'),
            ],
            'display_name' => [
                'required',
                'string',
                'max:255',
            ],
            'description' => [
                'nullable',
                'string',
                'max:1000',
            ],
            'guard_name' => [
                'nullable',
                'string',
                'max:255',
            ],
            'priority' => [
                'nullable',
                'integer',
                'min:0',
                'max:1000',
            ],
            'is_system' => [
                'nullable',
                'boolean',
            ],
            'status' => [
                'required',
                'string',
                Rule::in(RoleStatus::values()),
            ],
            'permissions' => [
                'nullable',
                'array',
            ],
            'permissions.*' => [
                'integer',
                'exists:permissions,id',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => __('Role Name'),
            'display_name' => __('Display Name'),
            'description' => __('Description'),
            'guard_name' => __('Guard Name'),
            'priority' => __('Priority'),
            'is_system' => __('System Role'),
            'status' => __('Status'),
            'permissions' => __('Permissions'),
            'permissions.*' => __('Permission'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => __('The role name field is required.'),
            'name.unique' => __('A role with this name already exists.'),
            'name.alpha_dash' => __('The role name may only contain letters, numbers, dashes and underscores.'),
            'display_name.required' => __('The display name field is required.'),
            'status.required' => __('The status field is required.'),
            'status.in' => __('The selected status is invalid.'),
            'priority.min' => __('The priority must be at least 0.'),
            'priority.max' => __('The priority may not be greater than 999.'),
            'permissions.array' => __('The permissions must be an array.'),
            'permissions.*.exists' => __('One or more selected permissions are invalid.'),
        ];
    }
}

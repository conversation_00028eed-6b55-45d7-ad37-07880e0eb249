<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\ChatBot\Models\KnowledgeBase;
use Modules\ChatBot\Models\Query;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Jobs\RAGFileProcessingJob;
use Modules\ChatBot\Jobs\QueryProcessingJob;
use Modules\User\Models\User;
use Illuminate\Support\Facades\Redis;

class TestRAGJob extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:rag-job {--type=file : Type of test (file|query)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test RAG job dispatch to Redis queue for Python service';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->option('type');

        if ($type === 'query') {
            return $this->testQueryEmbedding();
        } else {
            return $this->testFileProcessing();
        }
    }

    /**
     * Test file processing RAG job.
     */
    private function testFileProcessing()
    {
        $this->info('Testing RAG File Processing Job dispatch to Redis queue...');

        // Create a test knowledge base
        $kb = KnowledgeBase::create([
            'name' => 'Test KB for Python - ' . now()->format('Y-m-d H:i:s'),
            'description' => 'Test knowledge base for Python RAG processing',
            'type' => 'text',
            'status' => 'ready',
            'owner_id' => 1,
            'owner_type' => 'App\\Models\\User',
            'metadata' => ['test' => true, 'created_by_command' => true]
        ]);

        $this->info("Created KB ID: {$kb->id}");

        // Check Redis queue before
        $queueName = 'rag-processing';
        $beforeCount = Redis::llen($queueName);
        $this->info("Queue length before: {$beforeCount}");

        // Create and dispatch RAG job
        $job = new RAGFileProcessingJob([$kb->id], 1, 'App\\Models\\User', 'command_test_' . time());
        dispatch($job);
        $this->info('RAG job dispatched');

        // Wait a moment
        sleep(2);

        // Check Redis queue after
        $afterCount = Redis::llen($queueName);
        $this->info("Queue length after: {$afterCount}");

        // Check queue content
        $queueContent = Redis::lrange($queueName, 0, -1);
        $this->info("Queue content count: " . count($queueContent));

        if (count($queueContent) > 0) {
            $this->info('🎉 SUCCESS: Job found in Redis queue for Python!');
            $payload = json_decode($queueContent[0], true);

            if ($payload) {
                $this->table(['Field', 'Value'], [
                    ['Task ID', $payload['taskId'] ?? 'N/A'],
                    ['Operation', $payload['operation'] ?? 'N/A'],
                    ['Type', $payload['type'] ?? 'N/A'],
                    ['Owner ID', $payload['ownerId'] ?? 'N/A'],
                    ['Webhook URL', $payload['webhookUrl'] ?? 'N/A'],
                    ['Files count', isset($payload['files']) ? count($payload['files']) : 0],
                ]);

                $this->info('Full payload for Python service:');
                $this->line(json_encode($payload, JSON_PRETTY_PRINT));
            }
        } else {
            $this->warn('No jobs found in Redis queue');
        }

        return 0;
    }

    /**
     * Test query embedding workflow.
     */
    private function testQueryEmbedding()
    {
        $this->info('Testing Query Embedding workflow...');

        // Get or create test user
        $user = User::first();
        if (!$user) {
            $this->error('No users found in database. Please create a user first.');
            return 1;
        }

        // Get or create test bot
        $bot = Bot::first();
        if (!$bot) {
            $this->error('No bots found in database. Please create a bot first.');
            return 1;
        }

        // Create test conversation
        $conversation = Conversation::create([
            'uuid' => \Illuminate\Support\Str::uuid(),
            'bot_id' => $bot->id,
            'user_id' => $user->id,
            'title' => 'Test Conversation for Query Embedding - ' . now()->format('Y-m-d H:i:s'),
            'status' => 'active',
            'metadata' => ['test' => true, 'created_by_command' => true]
        ]);

        $this->info("Created Conversation ID: {$conversation->id}");

        // Create test user message
        $userMessage = Message::create([
            'uuid' => \Illuminate\Support\Str::uuid(),
            'conversation_id' => $conversation->id,
            'role' => 'user',
            'content' => 'What is Laravel and how does it work?',
            'status' => 'completed',
            'metadata' => ['test' => true, 'created_by_command' => true]
        ]);

        $this->info("Created User Message ID: {$userMessage->id}");

        // Create test assistant message
        $assistantMessage = Message::create([
            'uuid' => \Illuminate\Support\Str::uuid(),
            'conversation_id' => $conversation->id,
            'role' => 'assistant',
            'content' => '',
            'status' => 'pending',
            'metadata' => ['test' => true, 'created_by_command' => true]
        ]);

        $this->info("Created Assistant Message ID: {$assistantMessage->id}");

        // Create query for embedding
        $query = Query::create([
            'uuid' => \Illuminate\Support\Str::uuid(),
            'question' => $userMessage->content,
            'owner_id' => $user->id,
            'owner_type' => get_class($user),
            'bot_id' => $bot->id,
            'conversation_id' => $conversation->id,
            'message_id' => $userMessage->id,
            'file_ids' => null, // No specific files for this test
            'top_k' => 5,
            'collection' => 'documents',
            'status' => 'pending',
            'metadata' => [
                'test' => true,
                'created_by_command' => true,
                'assistant_message_id' => $assistantMessage->id
            ]
        ]);

        $this->info("Created Query ID: {$query->id}, UUID: {$query->uuid}");

        // Check Redis queue before
        $queueName = 'rag-processing';
        $beforeCount = Redis::llen($queueName);
        $this->info("Queue length before: {$beforeCount}");

        // Dispatch query processing job
        QueryProcessingJob::dispatch($query);
        $this->info('Query processing job dispatched');

        // Wait a moment
        sleep(2);

        // Check Redis queue after
        $afterCount = Redis::llen($queueName);
        $this->info("Queue length after: {$afterCount}");

        // Check queue content
        $queueContent = Redis::lrange($queueName, 0, -1);
        $this->info("Queue content count: " . count($queueContent));

        if (count($queueContent) > 0) {
            $this->info('🎉 SUCCESS: Query job found in Redis queue for Python!');
            $payload = json_decode($queueContent[0], true);

            if ($payload) {
                $this->table(['Field', 'Value'], [
                    ['Task ID', $payload['taskId'] ?? 'N/A'],
                    ['Type', $payload['type'] ?? 'N/A'],
                    ['Query ID', $payload['queryId'] ?? 'N/A'],
                    ['Question', $payload['question'] ?? 'N/A'],
                    ['Owner ID', $payload['ownerId'] ?? 'N/A'],
                    ['Collection', $payload['collection'] ?? 'N/A'],
                    ['Top K', $payload['topK'] ?? 'N/A'],
                    ['Webhook URL', $payload['webhookUrl'] ?? 'N/A'],
                    ['File IDs', isset($payload['fileIds']) ? json_encode($payload['fileIds']) : 'null'],
                ]);

                $this->info('Full payload for Python service:');
                $this->line(json_encode($payload, JSON_PRETTY_PRINT));
            }
        } else {
            $this->warn('No jobs found in Redis queue');
        }

        // Refresh query to check status
        $query->refresh();
        $this->info("Query status after dispatch: {$query->status}");

        return 0;
    }
}

<?php

namespace Modules\ActiveCode\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\ActiveCode\Models\ActiveCode;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\Modules\ActiveCode\Models\ActiveCode>
 */
class ActiveCodeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = ActiveCode::class;

    /**
     * String constants cho code types thay vì enum
     */
    private const CODE_TYPES = [
        'verification',
        'registration_verification',
        'login_otp',
        'email_verification',
        'phone_verification',
        'password_reset',
        'two_factor_auth',
        'account_activation',
        'security_verification'
    ];

    /**
     * Define the model's default state.
     * Chỉ sử dụng các trường có trong database schema thực tế
     */
    public function definition(): array
    {
        $type = $this->faker->randomElement(self::CODE_TYPES);

        return [
            'code' => $this->generateCodeByType($type),
            'type' => $type,
            'identifier' => $this->faker->randomElement([
                $this->faker->safeEmail(),
                $this->faker->phoneNumber(),
                'user_' . $this->faker->numberBetween(1, 1000)
            ]),
            'expires_at' => $this->faker->dateTimeBetween('now', '+1 hour'),
            'used_at' => null,
            'attempts' => $this->faker->numberBetween(0, 2),
        ];
    }

    /**
     * Generate code based on type
     */
    private function generateCodeByType(string $type): string
    {
        // Numeric codes for OTP and SMS-based verification
        $numericTypes = ['login_otp', 'two_factor_auth', 'phone_verification'];

        if (in_array($type, $numericTypes)) {
            return $this->faker->numerify('######');
        }

        // Alphanumeric codes for email verification
        return strtoupper($this->faker->bothify('??????'));
    }

    /**
     * Create an expired code.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => $this->faker->dateTimeBetween('-2 hours', '-1 hour'),
        ]);
    }

    /**
     * Create a used code.
     */
    public function used(): static
    {
        return $this->state(fn (array $attributes) => [
            'used_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
            'attempts' => $this->faker->numberBetween(1, 5),
        ]);
    }

    /**
     * Create a valid code (not expired, not used).
     */
    public function valid(): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => $this->faker->dateTimeBetween('now', '+1 hour'),
            'used_at' => null,
            'attempts' => 0,
        ]);
    }

    /**
     * Create a registration verification code.
     */
    public function registrationVerification(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'registration_verification',
            'code' => strtoupper($this->faker->bothify('??????')),
            'expires_at' => now()->addMinutes(30),
        ]);
    }

    /**
     * Create a login OTP code.
     */
    public function loginOtp(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'login_otp',
            'code' => $this->faker->numerify('######'),
            'expires_at' => now()->addMinutes(5),
        ]);
    }

    /**
     * Create an email verification code.
     */
    public function emailVerification(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'email_verification',
            'code' => strtoupper($this->faker->bothify('??????')),
            'expires_at' => now()->addHour(),
        ]);
    }

    /**
     * Create a password reset code.
     */
    public function passwordReset(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'password_reset',
            'code' => strtoupper($this->faker->bothify('????????')),
            'expires_at' => now()->addMinutes(15),
        ]);
    }

    /**
     * Create a phone verification code.
     */
    public function phoneVerification(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'phone_verification',
            'code' => $this->faker->numerify('####'),
            'expires_at' => now()->addMinutes(10),
        ]);
    }

    /**
     * Create a two-factor authentication code.
     */
    public function twoFactorAuth(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'two_factor_auth',
            'code' => $this->faker->numerify('######'),
            'expires_at' => now()->addMinutes(5),
        ]);
    }

    /**
     * Create an account activation code.
     */
    public function accountActivation(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'account_activation',
            'code' => strtoupper($this->faker->bothify('????????')),
            'expires_at' => now()->addDay(),
        ]);
    }

    /**
     * Create a security verification code.
     */
    public function securityVerification(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'security_verification',
            'code' => strtoupper($this->faker->bothify('????????')),
            'expires_at' => now()->addMinutes(10),
        ]);
    }

    /**
     * Create code for specific identifier.
     */
    public function forIdentifier(string $identifier): static
    {
        return $this->state(fn (array $attributes) => [
            'identifier' => $identifier,
        ]);
    }

    /**
     * Create code with specific email.
     */
    public function forEmail(string $email): static
    {
        return $this->state(fn (array $attributes) => [
            'identifier' => $email,
        ]);
    }

    /**
     * Create code with specific phone.
     */
    public function forPhone(string $phone): static
    {
        return $this->state(fn (array $attributes) => [
            'identifier' => $phone,
        ]);
    }

    /**
     * Create code with specific type.
     */
    public function ofType(string $type): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => $type,
            'code' => $this->generateCodeByType($type),
        ]);
    }

    /**
     * Create code with specific attempts count.
     */
    public function withAttempts(int $attempts): static
    {
        return $this->state(fn (array $attributes) => [
            'attempts' => $attempts,
        ]);
    }
}

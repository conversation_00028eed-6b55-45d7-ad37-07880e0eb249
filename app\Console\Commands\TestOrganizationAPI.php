<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\User\Models\User;
use <PERSON>mon\JWTAuth\Facades\JWTAuth;

class TestOrganizationAPI extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:organization-api {user_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test organization API endpoints';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id') ?? 1;
        
        $user = User::find($userId);
        
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return;
        }

        $this->info("Testing organization API for user: {$user->email}");
        
        try {
            // Generate JWT token for user
            $token = JWTAuth::fromUser($user);
            $this->info("Generated JWT token: " . substr($token, 0, 50) . "...");
            
            // Test API endpoint using HTTP client
            $response = $this->makeAPIRequest('/api/v1/auth/organizations', $token);
            
            $this->info("API Response Status: " . $response['status']);
            $this->info("API Response Body: " . $response['body']);
            
        } catch (\Exception $e) {
            $this->error("✗ Failed to test API:");
            $this->error($e->getMessage());
            $this->error("File: {$e->getFile()}:{$e->getLine()}");
        }
    }
    
    private function makeAPIRequest($endpoint, $token)
    {
        $baseUrl = config('app.url');
        $url = $baseUrl . $endpoint;
        
        $this->info("Making request to: " . $url);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $token,
            'Accept: application/json',
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new \Exception("CURL Error: " . $error);
        }
        
        return [
            'status' => $httpCode,
            'body' => $response
        ];
    }
}

<?php

namespace Modules\Currency\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Role\Models\Permission;
use Modules\Role\Models\Role;

class CurrencyPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedCurrencyPermissions();
        $this->assignPermissionsToSuperAdmin();
    }

    /**
     * Seed currency module permissions.
     */
    private function seedCurrencyPermissions(): void
    {
        $permissions = [
            [
                'name' => 'currency.view',
                'display_name' => 'View Currencies',
                'description' => 'Permission to view currencies list and details',
                'module_name' => 'currency',
                'sort_order' => 1,
                'guard_name' => 'api',
            ],
            [
                'name' => 'currency.create',
                'display_name' => 'Create Currencies',
                'description' => 'Permission to create new currencies',
                'module_name' => 'currency',
                'sort_order' => 2,
                'guard_name' => 'api',
            ],
            [
                'name' => 'currency.edit',
                'display_name' => 'Edit Currencies',
                'description' => 'Permission to update existing currencies',
                'module_name' => 'currency',
                'sort_order' => 3,
                'guard_name' => 'api',
            ],
            [
                'name' => 'currency.delete',
                'display_name' => 'Delete Currencies',
                'description' => 'Permission to soft delete and restore currencies',
                'module_name' => 'currency',
                'sort_order' => 4,
                'guard_name' => 'api',
            ],
            [
                'name' => 'currency.destroy',
                'display_name' => 'Destroy Currencies',
                'description' => 'Permission to permanently delete currencies',
                'module_name' => 'currency',
                'sort_order' => 5,
                'guard_name' => 'api',
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => 'api'],
                array_merge($permission, ['guard_name' => 'api'])
            );
        }
    }

    /**
     * Assign currency permissions to super-admin role.
     */
    private function assignPermissionsToSuperAdmin(): void
    {
        $superAdmin = Role::where('name', 'super-admin')->where('guard_name', 'api')->first();

        if ($superAdmin) {
            $currencyPermissions = Permission::where('module_name', 'currency')->where('guard_name', 'api')->get();
            
            // Gán tất cả quyền currency cho super-admin
            foreach ($currencyPermissions as $permission) {
                if (!$superAdmin->hasPermissionTo($permission->name)) {
                    $superAdmin->givePermissionTo($permission->name);
                }
            }
        }
    }
}

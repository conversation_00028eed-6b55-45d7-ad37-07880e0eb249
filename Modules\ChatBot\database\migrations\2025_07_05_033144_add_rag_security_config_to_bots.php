<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\ChatBot\Models\Bot;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing bots with secure RAG configuration
        $bots = Bot::all();

        foreach ($bots as $bot) {
            $metadata = $bot->metadata ?? [];

            // Add secure RAG configuration if not exists
            if (!isset($metadata['rag_config'])) {
                $metadata['rag_config'] = [
                    'enabled' => false, // RAG disabled by default for security
                    'auto_query' => false, // No automatic RAG triggering
                    'top_k' => 5, // Maximum 5 results
                    'collection' => 'documents',
                    'allow_user_file_selection' => false, // Users cannot specify file_ids
                    'max_tokens_limit' => 2000, // Maximum tokens per request
                    'max_temperature' => 1.0, // Maximum temperature allowed
                    'min_question_length' => 10, // Minimum question length for RAG
                ];
            }

            // Add parameter limits if not exists
            if (!isset($metadata['parameter_limits'])) {
                $metadata['parameter_limits'] = [
                    'max_temperature' => 1.5,
                    'max_tokens_limit' => 4000,
                    'max_top_k' => 10,
                ];
            }

            $bot->update(['metadata' => $metadata]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove RAG configuration from all bots
        $bots = Bot::all();

        foreach ($bots as $bot) {
            $metadata = $bot->metadata ?? [];

            // Remove RAG config
            unset($metadata['rag_config']);
            unset($metadata['parameter_limits']);

            $bot->update(['metadata' => $metadata]);
        }
    }
};

<?php

namespace Modules\Auth\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\User\Models\User;

class UserSocialAccount extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'user_social_accounts';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'provider',
        'provider_id',
        'provider_email',
        'provider_name',
        'provider_nickname',
        'provider_avatar',
        'access_token',
        'refresh_token',
        'token_expires_at',
        'provider_data',
        'last_used_at',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'provider_data' => 'array',
        'token_expires_at' => 'datetime',
        'last_used_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'access_token',
        'refresh_token',
    ];

    /**
     * Relationship: Social account belongs to a user.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope: Filter by provider.
     */
    public function scopeProvider($query, string $provider)
    {
        return $query->where('provider', $provider);
    }

    /**
     * Scope: Filter by provider ID.
     */
    public function scopeProviderId($query, string $providerId)
    {
        return $query->where('provider_id', $providerId);
    }

    /**
     * Scope: Recently used accounts.
     */
    public function scopeRecentlyUsed($query, int $days = 30)
    {
        return $query->where('last_used_at', '>=', now()->subDays($days));
    }

    /**
     * Check if the OAuth token is expired.
     */
    public function isTokenExpired(): bool
    {
        return $this->token_expires_at && $this->token_expires_at->isPast();
    }

    /**
     * Update last used timestamp.
     */
    public function markAsUsed(): void
    {
        $this->update(['last_used_at' => now()]);
    }

    /**
     * Get the provider display name.
     */
    public function getProviderDisplayNameAttribute(): string
    {
        return match ($this->provider) {
            'google' => 'Google',
            'facebook' => 'Facebook',
            'twitter' => 'Twitter',
            'apple' => 'Apple',
            'telegram' => 'Telegram',
            'zalo' => 'Zalo',
            default => ucfirst($this->provider),
        };
    }

    /**
     * Get the provider icon class.
     */
    public function getProviderIconAttribute(): string
    {
        return match ($this->provider) {
            'google' => 'fab fa-google',
            'facebook' => 'fab fa-facebook',
            'twitter' => 'fab fa-twitter',
            'apple' => 'fab fa-apple',
            'telegram' => 'fab fa-telegram',
            'zalo' => 'fas fa-z',
            default => 'fas fa-user',
        };
    }

    /**
     * Find social account by provider and provider ID.
     */
    public static function findByProvider(string $provider, string $providerId): ?self
    {
        return static::where('provider', $provider)
                    ->where('provider_id', $providerId)
                    ->first();
    }

    /**
     * Create or update social account.
     */
    public static function createOrUpdate(array $data): self
    {
        return static::updateOrCreate(
            [
                'provider' => $data['provider'],
                'provider_id' => $data['provider_id'],
            ],
            $data
        );
    }
}

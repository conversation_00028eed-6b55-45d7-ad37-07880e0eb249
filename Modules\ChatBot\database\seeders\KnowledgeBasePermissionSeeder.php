<?php

namespace Modules\ChatBot\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Role\Models\Permission;
use Modules\Role\Models\Role;

class KnowledgeBasePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->createKnowledgeBasePermissions();
        $this->assignPermissionsToSuperAdmin();
    }

    /**
     * Create knowledge base permissions.
     */
    private function createKnowledgeBasePermissions(): void
    {
        $permissions = [
            [
                'name' => 'knowledge-base.read',
                'display_name' => 'View Knowledge Bases',
                'description' => 'Permission to view knowledge bases list and details',
                'module_name' => 'knowledge-base',
                'sort_order' => 1,
                'guard_name' => 'api',
            ],
            [
                'name' => 'knowledge-base.create',
                'display_name' => 'Create Knowledge Bases',
                'description' => 'Permission to create new knowledge bases from text or files',
                'module_name' => 'knowledge-base',
                'sort_order' => 2,
                'guard_name' => 'api',
            ],
            [
                'name' => 'knowledge-base.update',
                'display_name' => 'Update Knowledge Bases',
                'description' => 'Permission to update existing knowledge bases',
                'module_name' => 'knowledge-base',
                'sort_order' => 3,
                'guard_name' => 'api',
            ],
            [
                'name' => 'knowledge-base.destroy',
                'display_name' => 'Delete Knowledge Bases',
                'description' => 'Permission to delete knowledge bases and bulk delete',
                'module_name' => 'knowledge-base',
                'sort_order' => 4,
                'guard_name' => 'api',
            ],
            [
                'name' => 'knowledge-base.upload',
                'display_name' => 'Upload Files',
                'description' => 'Permission to upload files for knowledge bases',
                'module_name' => 'knowledge-base',
                'sort_order' => 5,
                'guard_name' => 'api',
            ],
            [
                'name' => 'knowledge-base.bulk-upload',
                'display_name' => 'Bulk Upload Files',
                'description' => 'Permission to upload multiple files at once',
                'module_name' => 'knowledge-base',
                'sort_order' => 6,
                'guard_name' => 'api',
            ],
            [
                'name' => 'knowledge-base.retrain',
                'display_name' => 'Retrain Knowledge Bases',
                'description' => 'Permission to retrain knowledge bases',
                'module_name' => 'knowledge-base',
                'sort_order' => 7,
                'guard_name' => 'api',
            ],
            [
                'name' => 'knowledge-base.attach-to-bot',
                'display_name' => 'Attach to Bot',
                'description' => 'Permission to attach knowledge bases to bots',
                'module_name' => 'knowledge-base',
                'sort_order' => 8,
                'guard_name' => 'api',
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => 'api'],
                array_merge($permission, ['guard_name' => 'api'])
            );
        }
    }

    /**
     * Assign knowledge base permissions to super-admin role.
     */
    private function assignPermissionsToSuperAdmin(): void
    {
        $superAdmin = Role::where('name', 'super-admin')->where('guard_name', 'api')->first();

        if ($superAdmin) {
            $knowledgeBasePermissions = Permission::where('module_name', 'knowledge-base')->where('guard_name', 'api')->get();
            
            foreach ($knowledgeBasePermissions as $permission) {
                if (!$superAdmin->hasPermissionTo($permission->name)) {
                    $superAdmin->givePermissionTo($permission->name);
                }
            }
        }
    }
}

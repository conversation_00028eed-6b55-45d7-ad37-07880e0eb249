<?php

namespace Modules\Currency\Http\Controllers\Auth;

use Modules\Core\Traits\ResponseTrait;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Modules\Currency\Models\ExchangeRate;
use Modules\Currency\Http\Requests\ExchangeRateRequest;
use Modules\Currency\Http\Requests\BulkExchangeRateRequest;

class ExchangeRateController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {
        $this->middleware('role_or_permission:super-admin|currency.view')->only(['index', 'show', 'stale']);
        $this->middleware('role_or_permission:super-admin|currency.create')->only(['store']);
        $this->middleware('role_or_permission:super-admin|currency.edit')->only(['update', 'bulkUpdate', 'refresh']);
        $this->middleware('role_or_permission:super-admin|currency.delete')->only(['destroy']);
    }

    /**
     * Display a listing of exchange rates.
     */
    public function index(Request $request): JsonResponse
    {
        $exchangeRates = ExchangeRate::query()
            ->with(['baseCurrency', 'targetCurrency'])
            ->latest('fetched_at')
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($exchangeRates, __('Exchange rates retrieved successfully.'));
    }

    /**
     * Store a newly created exchange rate.
     */
    public function store(ExchangeRateRequest $request): JsonResponse
    {
        $data = $request->validated();
        $data['fetched_at'] = now();

        // Create the main rate
        $exchangeRate = ExchangeRate::updateOrCreate(
            [
                'base_currency' => $data['base_currency'],
                'target_currency' => $data['target_currency']
            ],
            [
                'rate' => $data['rate'],
                'fetched_at' => $data['fetched_at']
            ]
        );

        // Create reverse rate if requested
        if ($request->boolean('create_reverse', true)) {
            ExchangeRate::updateOrCreate(
                [
                    'base_currency' => $data['target_currency'],
                    'target_currency' => $data['base_currency']
                ],
                [
                    'rate' => 1 / $data['rate'],
                    'fetched_at' => $data['fetched_at']
                ]
            );
        }

        return $this->successResponse($exchangeRate->load(['baseCurrency', 'targetCurrency']), __('Exchange rate created successfully.'), 201);
    }

    /**
     * Display the specified exchange rate.
     */
    public function show(ExchangeRate $exchangeRate): JsonResponse
    {
        return $this->successResponse(
            $exchangeRate->load(['baseCurrency', 'targetCurrency']), 
            __('Exchange rate retrieved successfully.')
        );
    }

    /**
     * Update the specified exchange rate.
     */
    public function update(ExchangeRateRequest $request, ExchangeRate $exchangeRate): JsonResponse
    {
        $data = $request->validated();
        $data['fetched_at'] = now();

        $exchangeRate->update($data);

        // Update reverse rate if requested
        if ($request->boolean('update_reverse', true)) {
            ExchangeRate::updateOrCreate(
                [
                    'base_currency' => $data['target_currency'],
                    'target_currency' => $data['base_currency']
                ],
                [
                    'rate' => 1 / $data['rate'],
                    'fetched_at' => $data['fetched_at']
                ]
            );
        }

        return $this->successResponse($exchangeRate->fresh()->load(['baseCurrency', 'targetCurrency']), __('Exchange rate updated successfully.'));
    }

    /**
     * Remove the specified exchange rate.
     */
    public function destroy(ExchangeRate $exchangeRate): JsonResponse
    {
        $baseCurrency = $exchangeRate->base_currency;
        $targetCurrency = $exchangeRate->target_currency;

        $exchangeRate->delete();

        // Delete reverse rate
        ExchangeRate::where('base_currency', $targetCurrency)
            ->where('target_currency', $baseCurrency)
            ->delete();

        return $this->successResponse($exchangeRate->id, __('Exchange rate deleted successfully.'));
    }

    /**
     * Bulk update exchange rates.
     */
    public function bulkUpdate(BulkExchangeRateRequest $request): JsonResponse
    {
        $rates = $request->validated('rates');
        $updatedCount = 0;

        foreach ($rates as $rateData) {
            ExchangeRate::updateOrCreate(
                [
                    'base_currency' => $rateData['base_currency'],
                    'target_currency' => $rateData['target_currency']
                ],
                [
                    'rate' => $rateData['rate'],
                    'fetched_at' => now()
                ]
            );

            // Create reverse rate
            ExchangeRate::updateOrCreate(
                [
                    'base_currency' => $rateData['target_currency'],
                    'target_currency' => $rateData['base_currency']
                ],
                [
                    'rate' => 1 / $rateData['rate'],
                    'fetched_at' => now()
                ]
            );

            $updatedCount++;
        }

        return $this->successResponse(
            ['updated_count' => $updatedCount],
            __(':count exchange rates updated successfully.', ['count' => $updatedCount])
        );
    }

    /**
     * Refresh exchange rates from external API.
     */
    public function refresh(Request $request): JsonResponse
    {
        $baseCurrency = $request->input('base_currency', 'USD');
        $targetCurrencies = $request->input('target_currencies', ['VND', 'EUR', 'GBP', 'JPY']);
        $apiProvider = $request->input('api_provider', setting('currency.currency_api_default', 'freecurrency'));

        $updatedCount = 0;
        
        foreach ($targetCurrencies as $targetCurrency) {
            if ($baseCurrency === $targetCurrency) continue;

            // Get rate from external API or simulate
            $rate = $this->getExchangeRate($baseCurrency, $targetCurrency, $apiProvider);

            ExchangeRate::updateOrCreate(
                [
                    'base_currency' => $baseCurrency,
                    'target_currency' => $targetCurrency
                ],
                [
                    'rate' => $rate,
                    'fetched_at' => now()
                ]
            );

            // Create reverse rate
            ExchangeRate::updateOrCreate(
                [
                    'base_currency' => $targetCurrency,
                    'target_currency' => $baseCurrency
                ],
                [
                    'rate' => 1 / $rate,
                    'fetched_at' => now()
                ]
            );

            $updatedCount++;
        }

        return $this->successResponse(
            ['updated_count' => $updatedCount, 'base_currency' => $baseCurrency, 'api_provider' => $apiProvider],
            __('Exchange rates refreshed successfully.')
        );
    }

    /**
     * Get stale exchange rates (older than 24 hours).
     */
    public function stale(): JsonResponse
    {
        $staleRates = ExchangeRate::where('fetched_at', '<', now()->subDay())
            ->orWhereNull('fetched_at')
            ->with(['baseCurrency', 'targetCurrency'])
            ->get();

        return $this->successResponse($staleRates, __('Stale exchange rates retrieved successfully.'));
    }

    /**
     * Get exchange rate from external API or simulate.
     */
    private function getExchangeRate(string $baseCurrency, string $targetCurrency, string $apiProvider): float
    {
        try {
            // Use ExchangeRateService to get real rates
            $exchangeRateService = app(\Modules\Currency\Services\ExchangeRateService::class);
            $rate = $exchangeRateService->fetchExchangeRate($baseCurrency, $targetCurrency, $apiProvider);

            if ($rate !== null) {
                return $rate;
            }
        } catch (\Exception $e) {
            Log::error('Failed to fetch exchange rate', [
                'base' => $baseCurrency,
                'target' => $targetCurrency,
                'provider' => $apiProvider,
                'error' => $e->getMessage()
            ]);
        }

        // Fallback to simulated rates
        $simulatedRates = [
            'USD' => [
                'VND' => 23000 + rand(-500, 500),
                'EUR' => 0.85 + (rand(-50, 50) / 1000),
                'GBP' => 0.73 + (rand(-30, 30) / 1000),
                'JPY' => 110 + rand(-5, 5),
            ],
        ];

        return $simulatedRates[$baseCurrency][$targetCurrency] ?? 1.0;
    }
}

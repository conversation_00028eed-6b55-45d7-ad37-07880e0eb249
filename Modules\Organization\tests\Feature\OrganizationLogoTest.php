<?php

namespace Modules\Organization\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Modules\Organization\Models\Organization;
use Modules\User\Models\User;
use Spatie\Permission\Models\Permission;
use PHPUnit\Framework\Attributes\Test;

class OrganizationLogoTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');

        // Create permissions if they don't exist
        Permission::firstOrCreate(['name' => 'organization.create', 'guard_name' => 'api']);
        Permission::firstOrCreate(['name' => 'organization.manage', 'guard_name' => 'api']);
    }

    #[Test]
    public function it_returns_default_logo_url_when_no_logo_is_set()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->create([
            'owner_id' => $user->id,
            'logo' => null
        ]);

        $this->assertEquals(
            asset('storage/organization-avatars/organization.png'),
            $organization->logo_url
        );
    }

    #[Test]
    public function it_returns_storage_url_when_logo_is_set()
    {
        $user = User::factory()->create();
        $organization = Organization::factory()->create([
            'owner_id' => $user->id,
            'logo' => 'organization-logos/test-logo.png'
        ]);

        $this->assertEquals(
            Storage::url('organization-logos/test-logo.png'),
            $organization->logo_url
        );
    }

    #[Test]
    public function it_returns_full_url_when_logo_is_external_url()
    {
        $user = User::factory()->create();
        $externalUrl = 'https://example.com/logo.png';
        $organization = Organization::factory()->create([
            'owner_id' => $user->id,
            'logo' => $externalUrl
        ]);

        $this->assertEquals($externalUrl, $organization->logo_url);
    }

    #[Test]
    public function it_can_upload_logo_to_temp_successfully()
    {
        $user = User::factory()->create();

        $file = UploadedFile::fake()->image('logo.png', 100, 100);

        $response = $this->actingAs($user, 'api')
            ->postJson("/api/v1/auth/organizations/upload-logo", [
                'logo' => $file
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'logo_path',
                    'logo_url'
                ]
            ]);

        // Check if file was stored in temp directory
        $logoPath = $response->json('data.logo_path');
        Storage::disk('public')->assertExists($logoPath);
        $this->assertStringContains('organization-logos/temp/', $logoPath);
    }

    #[Test]
    public function it_creates_organization_with_logo_from_temp()
    {
        $user = User::factory()->create();
        $user->givePermissionTo('organization.create', 'api');

        // First upload logo to temp
        $file = UploadedFile::fake()->image('logo.png', 100, 100);
        $uploadResponse = $this->actingAs($user, 'api')
            ->postJson("/api/v1/auth/organizations/upload-logo", [
                'logo' => $file
            ]);

        $tempLogoPath = $uploadResponse->json('data.logo_path');

        // Then create organization with temp logo path
        $organizationData = [
            'name' => 'Test Organization',
            'email' => '<EMAIL>',
            'logo' => $tempLogoPath
        ];

        $response = $this->actingAs($user, 'api')
            ->postJson("/api/v1/auth/organizations", $organizationData);

        $response->assertStatus(201);

        $organization = Organization::latest()->first();

        // Check if logo was moved from temp to organization directory
        $this->assertNotNull($organization->logo);
        $this->assertStringContains("organization-logos/{$organization->uuid}/", $organization->logo);
        Storage::disk('public')->assertExists($organization->logo);

        // Check if temp file was removed
        Storage::disk('public')->assertMissing($tempLogoPath);
    }

    #[Test]
    public function it_validates_logo_file_requirements()
    {
        $user = User::factory()->create();

        // Test without file
        $response = $this->actingAs($user, 'api')
            ->postJson("/api/v1/auth/organizations/upload-logo", []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['logo']);

        // Test with invalid file type
        $file = UploadedFile::fake()->create('document.pdf', 1000);

        $response = $this->actingAs($user, 'api')
            ->postJson("/api/v1/auth/organizations/upload-logo", [
                'logo' => $file
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['logo']);
    }

    #[Test]
    public function it_allows_any_authenticated_user_to_upload_logo()
    {
        $user = User::factory()->create();
        $file = UploadedFile::fake()->image('logo.png', 100, 100);

        $response = $this->actingAs($user, 'api')
            ->postJson("/api/v1/auth/organizations/upload-logo", [
                'logo' => $file
            ]);

        $response->assertStatus(200);
    }

    #[Test]
    public function it_requires_authentication_for_logo_upload()
    {
        $file = UploadedFile::fake()->image('logo.png', 100, 100);

        $response = $this->postJson("/api/v1/auth/organizations/upload-logo", [
            'logo' => $file
        ]);

        $response->assertStatus(401);
    }
}

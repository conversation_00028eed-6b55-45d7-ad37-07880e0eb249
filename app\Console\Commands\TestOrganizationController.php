<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\Organization\Models\Organization;
use Modules\User\Models\User;
use Modules\Organization\Http\Controllers\Auth\OrganizationController;
use Modules\Organization\Http\Requests\Organization\ListOrganizationRequest;
use Illuminate\Http\Request;

class TestOrganizationController extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:organization-controller {user_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test organization controller with different users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id') ?? 1;
        
        $user = User::find($userId);
        
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return;
        }

        $this->info("Testing organization controller for user: {$user->email}");
        $this->info("User roles: " . $user->roles()->pluck('name')->implode(', '));
        
        try {
            // Create mock request
            $request = new Request();
            $listRequest = new ListOrganizationRequest();
            $listRequest->setUserResolver(function() use ($user) {
                return $user;
            });
            
            // Test authorization
            $canAuthorize = $listRequest->authorize();
            $this->info("Request authorize: " . ($canAuthorize ? 'YES' : 'NO'));
            
            if (!$canAuthorize) {
                $this->error("User cannot access organization list");
                return;
            }
            
            // Test controller logic manually
            $query = Organization::query()
                ->with(['owner:id,first_name,last_name', 'parent:id,name']);

            // Apply same logic as controller
            if (!$user->hasRole(['super-admin', 'admin'], 'api') && !$user->hasPermissionTo('organization.manage', 'api')) {
                $this->info("Applying user-specific filter...");
                $query->where(function ($q) use ($user) {
                    $q->where('owner_id', $user->id)
                      ->orWhereHas('members', function ($memberQuery) use ($user) {
                          $memberQuery->where('user_id', $user->id);
                      });
                });
            } else {
                $this->info("User has admin privileges - showing all organizations");
            }

            $organizations = $query->get();
            
            $this->info("Organizations visible to user: " . $organizations->count());
            
            foreach ($organizations as $org) {
                $isMember = $org->members()->where('user_id', $user->id)->exists();
                $isOwner = $org->owner_id == $user->id;
                $access = [];
                if ($isOwner) $access[] = 'Owner';
                if ($isMember) $access[] = 'Member';
                
                $this->line("- ID: {$org->id}, Name: {$org->name}, Access: " . implode(', ', $access));
            }
            
        } catch (\Exception $e) {
            $this->error("✗ Failed to test controller:");
            $this->error($e->getMessage());
            $this->error("File: {$e->getFile()}:{$e->getLine()}");
        }
    }
}

<?php

namespace Modules\Auth\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AuthNotificationTemplatesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedRemainingTemplates();
    }

    /**
     * Seed remaining Auth notification templates.
     */
    private function seedRemainingTemplates(): void
    {
        // Get notification types
        $notificationTypes = DB::table('notification_types')
            ->whereIn('key', [
                'user.verified', 'verification_code_sent', 'user.logged-in', 
                'password_reset', 'password_changed'
            ])
            ->get()
            ->keyBy('key');

        $templates = [];

        $this->command->info('Creating remaining Auth notification templates...');

        // User Verified Templates
        if (isset($notificationTypes['user.verified'])) {
            $userVerifiedType = $notificationTypes['user.verified'];

            // English templates
            $templates[] = [
                'notification_type_id' => $userVerifiedType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => 'Email Verified Successfully',
                'content' => 'Your email has been verified successfully. Welcome to {app_name}!',
                'variables' => json_encode(['user_name', 'app_name']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $userVerifiedType->id,
                'channel' => 'email',
                'locale' => 'en',
                'subject' => 'Email Verified - Welcome to {app_name}!',
                'title' => 'Email Verification Successful',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verified - {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">✅ Email Verified!</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Hi <strong>{user_name}</strong>,</p>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            Congratulations! Your email has been verified successfully. Your account is now fully activated 
            and you can enjoy all features of {app_name}.
        </p>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="{dashboard_url}" style="background: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                Start Exploring
            </a>
        </div>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            Thank you for joining us!
        </p>
        
        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">
        
        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Best regards,<br>
            The {app_name} Team
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'app_name', 'dashboard_url']),
                'settings' => json_encode(['from_name' => '{app_name} Team']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // Vietnamese templates
            $templates[] = [
                'notification_type_id' => $userVerifiedType->id,
                'channel' => 'database',
                'locale' => 'vi',
                'subject' => null,
                'title' => 'Xác thực email thành công',
                'content' => 'Email của bạn đã được xác thực thành công. Chào mừng đến với {app_name}!',
                'variables' => json_encode(['user_name', 'app_name']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $userVerifiedType->id,
                'channel' => 'email',
                'locale' => 'vi',
                'subject' => 'Email đã được xác thực - Chào mừng đến với {app_name}!',
                'title' => 'Xác thực email thành công',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email đã được xác thực - {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">✅ Email đã được xác thực!</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Xin chào <strong>{user_name}</strong>,</p>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            Chúc mừng! Email của bạn đã được xác thực thành công. Tài khoản của bạn hiện đã được kích hoạt đầy đủ 
            và bạn có thể tận hưởng tất cả các tính năng của {app_name}.
        </p>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="{dashboard_url}" style="background: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                Bắt đầu khám phá
            </a>
        </div>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            Cảm ơn bạn đã tham gia cùng chúng tôi!
        </p>
        
        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">
        
        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Trân trọng,<br>
            Đội ngũ {app_name}
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'app_name', 'dashboard_url']),
                'settings' => json_encode(['from_name' => 'Đội ngũ {app_name}']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Verification Code Sent Templates (OTP)
        if (isset($notificationTypes['verification_code_sent'])) {
            $verificationCodeType = $notificationTypes['verification_code_sent'];

            // English templates
            $templates[] = [
                'notification_type_id' => $verificationCodeType->id,
                'channel' => 'email',
                'locale' => 'en',
                'subject' => 'Your verification code - {app_name}',
                'title' => 'Email Verification Code',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verification Code - {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #007bff 0%, #6610f2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">🔐 Verification Code</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Hi <strong>{user_name}</strong>,</p>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            Your verification code for {app_name} is:
        </p>
        
        <div style="text-align: center; margin: 30px 0;">
            <div style="background: #007bff; color: white; padding: 20px; border-radius: 10px; font-size: 32px; font-weight: bold; letter-spacing: 8px; display: inline-block; min-width: 200px;">
                {code}
            </div>
        </div>
        
        <p style="font-size: 16px; margin-bottom: 20px; text-align: center;">
            <strong>This code will expire in {expires_in} minutes.</strong>
        </p>
        
        <p style="font-size: 14px; color: #666; margin-top: 20px;">
            If you didn\'t request this code, please ignore this email.
        </p>
        
        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">
        
        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Best regards,<br>
            The {app_name} Team
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'app_name', 'code', 'expires_in']),
                'settings' => json_encode(['from_name' => '{app_name} Team', 'priority' => 'high']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // Vietnamese templates
            $templates[] = [
                'notification_type_id' => $verificationCodeType->id,
                'channel' => 'email',
                'locale' => 'vi',
                'subject' => 'Mã xác thực của bạn - {app_name}',
                'title' => 'Mã xác thực email',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mã xác thực - {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #007bff 0%, #6610f2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">🔐 Mã xác thực</h1>
    </div>
    
    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Xin chào <strong>{user_name}</strong>,</p>
        
        <p style="font-size: 16px; margin-bottom: 20px;">
            Mã xác thực của bạn cho {app_name} là:
        </p>
        
        <div style="text-align: center; margin: 30px 0;">
            <div style="background: #007bff; color: white; padding: 20px; border-radius: 10px; font-size: 32px; font-weight: bold; letter-spacing: 8px; display: inline-block; min-width: 200px;">
                {code}
            </div>
        </div>
        
        <p style="font-size: 16px; margin-bottom: 20px; text-align: center;">
            <strong>Mã này sẽ hết hạn sau {expires_in} phút.</strong>
        </p>
        
        <p style="font-size: 14px; color: #666; margin-top: 20px;">
            Nếu bạn không yêu cầu mã này, vui lòng bỏ qua email này.
        </p>
        
        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">
        
        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Trân trọng,<br>
            Đội ngũ {app_name}
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'app_name', 'code', 'expires_in']),
                'settings' => json_encode(['from_name' => 'Đội ngũ {app_name}', 'priority' => 'high']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Insert all templates
        foreach ($templates as $template) {
            DB::table('notification_templates')->updateOrInsert(
                [
                    'notification_type_id' => $template['notification_type_id'],
                    'channel' => $template['channel'],
                    'locale' => $template['locale']
                ],
                $template
            );
        }

        $this->command->info('Remaining Auth notification templates seeded successfully. Total: ' . count($templates));
    }
}

<?php

namespace Modules\User\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

class BulkUserDestroyRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ids' => [
                'required',
                'array',
                'min:1',
            ],
            'ids.*' => [
                'required',
                'integer',
                Rule::exists('users', 'id')->whereNotNull('deleted_at'), // Only soft deleted users
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'ids' => __('User IDs'),
            'ids.*' => __('User ID'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ids.required' => __('Please select at least one user.'),
            'ids.array' => __('Invalid data format.'),
            'ids.min' => __('Please select at least one user.'),
            'ids.*.required' => __('User ID is required.'),
            'ids.*.integer' => __('User ID must be an integer.'),
            'ids.*.exists' => __('One or more selected users must be in trash to be permanently deleted.'),
        ];
    }
}

<?php

namespace Modules\Location\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Location\Models\GeoDivision;
use Modules\Location\Models\Country;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\Modules\Location\Models\GeoDivision>
 */
class GeoDivisionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = GeoDivision::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $types = ['province', 'state', 'region', 'district', 'city', 'county', 'municipality'];
        $type = $this->faker->randomElement($types);
        
        return [
            'code' => $this->faker->unique()->regexify('[A-Z]{2,3}[0-9]{2,4}'),
            'name' => $this->faker->city(),
            'native_name' => $this->faker->optional()->city(),
            'type' => $type,
            'country_id' => Country::factory(),
            'parent_id' => null, // Will be set by relationships
            'level' => $this->faker->numberBetween(1, 4),
            'path' => null, // Will be generated based on hierarchy
            'latitude' => $this->faker->optional()->latitude(),
            'longitude' => $this->faker->optional()->longitude(),
            'postal_code' => $this->faker->optional()->postcode(),
            'sort_order' => $this->faker->numberBetween(1, 100),
            'status' => $this->faker->randomElement(['active', 'inactive']),
        ];
    }

    /**
     * Indicate that the geo division is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the geo division is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'inactive',
        ]);
    }

    /**
     * Create a geo division with specific type.
     */
    public function type(string $type): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => $type,
        ]);
    }

    /**
     * Create a province level geo division.
     */
    public function province(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'province',
            'level' => 1,
            'parent_id' => null,
        ]);
    }

    /**
     * Create a district level geo division.
     */
    public function district(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'district',
            'level' => 2,
        ]);
    }

    /**
     * Create a city level geo division.
     */
    public function city(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'city',
            'level' => 3,
        ]);
    }

    /**
     * Create a geo division with specific country.
     */
    public function forCountry(int $countryId): static
    {
        return $this->state(fn (array $attributes) => [
            'country_id' => $countryId,
        ]);
    }

    /**
     * Create a geo division with specific parent.
     */
    public function withParent(int $parentId): static
    {
        return $this->state(fn (array $attributes) => [
            'parent_id' => $parentId,
        ]);
    }

    /**
     * Create a geo division with specific level.
     */
    public function level(int $level): static
    {
        return $this->state(fn (array $attributes) => [
            'level' => $level,
        ]);
    }
}

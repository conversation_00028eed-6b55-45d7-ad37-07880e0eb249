<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\Organization\Models\Organization;
use Modules\User\Models\User;
use Modules\Organization\Http\Requests\Organization\DestroyOrganizationRequest;

class TestDeleteWithUUID extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:delete-uuid {user_id} {uuid}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test organization deletion with UUID';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id');
        $uuid = $this->argument('uuid');
        
        $user = User::find($userId);
        
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return;
        }
        
        // Test route binding with UUID
        $model = new Organization();
        $organization = $model->resolveRouteBinding($uuid);
        
        if (!$organization) {
            $this->error("Organization with UUID {$uuid} not found");
            return;
        }

        $this->info("Testing organization deletion with UUID:");
        $this->info("User: {$user->email} (ID: {$user->id})");
        $this->info("Organization: {$organization->name} (ID: {$organization->id})");
        $this->info("Organization UUID: {$organization->uuid}");
        $this->info("Organization Owner ID: {$organization->owner_id}");
        
        try {
            // Test policy directly
            $canDelete = $user->can('delete', $organization);
            $this->info("Policy allows delete: " . ($canDelete ? 'YES' : 'NO'));
            
            // Test request authorization
            $request = new DestroyOrganizationRequest();
            $request->setUserResolver(function() use ($user) {
                return $user;
            });
            
            // Mock route parameter for space route with UUID
            $request->setRouteResolver(function() use ($organization) {
                return new class($organization) {
                    private $organization;
                    public function __construct($organization) {
                        $this->organization = $organization;
                    }
                    public function parameter($key) {
                        if ($key === 'space') {
                            return $this->organization;
                        }
                        return null;
                    }
                    public function parameters() {
                        return ['space' => $this->organization];
                    }
                    public function getName() {
                        return 'auth.space.destroy';
                    }
                };
            });
            
            $canAuthorize = $request->authorize();
            $this->info("Request authorize (space route with UUID): " . ($canAuthorize ? 'YES' : 'NO'));
            
            if ($canAuthorize) {
                $this->info("✓ User can delete this organization via space route with UUID");
            } else {
                $this->error("✗ User cannot delete this organization via space route with UUID");
                
                // Check specific reasons
                if ($user->id !== $organization->owner_id) {
                    $this->error("Reason: User is not the owner of this organization");
                    $this->error("User ID: {$user->id}, Owner ID: {$organization->owner_id}");
                } else {
                    $this->error("Reason: Unknown - user is owner but still denied");
                }
            }
            
        } catch (\Exception $e) {
            $this->error("✗ Failed to test deletion with UUID:");
            $this->error($e->getMessage());
            $this->error("File: {$e->getFile()}:{$e->getLine()}");
        }
    }
}

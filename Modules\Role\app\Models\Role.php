<?php

namespace Modules\Role\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Permission\Models\Role as Model;
use Illuminate\Database\Eloquent\SoftDeletes;

use Modules\Role\Database\Factories\RoleFactory;
use Modules\Role\Enums\RoleStatus;
use Spatie\Permission\PermissionRegistrar;

class Role extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'display_name',
        'guard_name',
        'description',
        'is_system',
        'priority',
        'status',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'is_system' => 'boolean',
        'priority' => 'integer',
        'status' => RoleStatus::class,
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = ['deleted_at', 'pivot'];

    /**
     * Scope a query to exclude system roles.
     */
    public function scopeNonSystem(Builder $query): Builder
    {
        return $query->where('is_system', false);
    }

    /**
     * Scope a query to only include system roles.
     */
    public function scopeSystem(Builder $query): Builder
    {
        return $query->where('is_system', true);
    }

    /**
     * Scope a query to filter by guard.
     */
    public function scopeForGuard(Builder $query, string $guard): Builder
    {
        return $query->where('guard_name', $guard);
    }

    /**
     * Scope a query to order by priority.
     */
    public function scopeByPriority(Builder $query): Builder
    {
        return $query->orderBy('priority')->orderBy('name');
    }

    /**
     * Scope a query to only include active roles.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', RoleStatus::Active);
    }

    /**
     * Scope a query to only include inactive roles.
     */
    public function scopeInactive(Builder $query): Builder
    {
        return $query->where('status', RoleStatus::Inactive);
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeStatus(Builder $query, RoleStatus $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Check if role is a system role.
     */
    public function isSystem(): bool
    {
        return $this->is_system;
    }

    /**
     * Check if role can be deleted.
     */
    public function canBeDeleted(): bool
    {
        return !$this->is_system && $this->users()->count() === 0;
    }

    /**
     * Check if role is active and can grant permissions.
     */
    public function isActive(): bool
    {
        return $this->status === RoleStatus::Active && !$this->trashed();
    }

    /**
     * Override hasPermissionTo to check role status and soft delete.
     */
    public function hasPermissionTo($permission, $guardName = null): bool
    {
        // If role is inactive or soft deleted, deny all permissions
        if (!$this->isActive()) {
            return false;
        }

        return parent::hasPermissionTo($permission, $guardName);
    }

    /**
     * Boot the model and set up event listeners for cache invalidation.
     */
    protected static function boot(): void
    {
        parent::boot();

        // Clear cache when role status changes
        static::updated(function ($role) {
            if ($role->isDirty('status')) {
                app(PermissionRegistrar::class)->forgetCachedPermissions();
            }
        });

        // Clear cache when role is soft deleted
        static::deleted(function ($role) {
            app(PermissionRegistrar::class)->forgetCachedPermissions();
        });

        // Clear cache when role is restored
        static::restored(function ($role) {
            app(PermissionRegistrar::class)->forgetCachedPermissions();
        });

        // Clear cache when role is force deleted
        static::forceDeleted(function ($role) {
            app(PermissionRegistrar::class)->forgetCachedPermissions();
        });
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): RoleFactory
    {
        return RoleFactory::new();
    }
}

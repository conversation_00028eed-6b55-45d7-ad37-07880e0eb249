<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\Role\Models\Permission;
use Modules\Role\Models\Role;
use Modules\User\Models\User;

class CheckOrganizationPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:organization-permissions {user_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check organization permissions for a user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id');
        
        if (!$userId) {
            $userId = $this->ask('Enter user ID to check');
        }

        $user = User::find($userId);
        
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return;
        }

        $this->info("Checking permissions for user: {$user->email}");
        $this->line('');

        // Check organization permissions
        $orgPermissions = Permission::where('module_name', 'organization')->get();
        
        $this->info('Organization Permissions in Database:');
        foreach ($orgPermissions as $permission) {
            $hasPermission = $user->hasPermissionTo($permission->name, 'api');
            $status = $hasPermission ? '✓' : '✗';
            $this->line("{$status} {$permission->name} (guard: {$permission->guard_name})");
        }

        $this->line('');
        
        // Check user roles
        $this->info('User Roles:');
        $userRoles = $user->roles()->where('guard_name', 'api')->get();
        foreach ($userRoles as $role) {
            $this->line("- {$role->name} (guard: {$role->guard_name})");
        }

        $this->line('');

        // Test specific permission
        $canCreate = $user->hasPermissionTo('organization.create', 'api');
        $this->info("Can create organization: " . ($canCreate ? 'YES' : 'NO'));

        // Test policy
        $canCreateViaPolicy = $user->can('create', \Modules\Organization\Models\Organization::class);
        $this->info("Can create via policy: " . ($canCreateViaPolicy ? 'YES' : 'NO'));

        // Test Gate directly
        $canCreateViaGate = \Illuminate\Support\Facades\Gate::forUser($user)->allows('create', \Modules\Organization\Models\Organization::class);
        $this->info("Can create via Gate: " . ($canCreateViaGate ? 'YES' : 'NO'));
    }
}

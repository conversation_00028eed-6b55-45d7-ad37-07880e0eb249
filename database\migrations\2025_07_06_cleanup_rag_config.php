<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\ChatBot\Models\Bot;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Clean up RAG config in all bots
        $bots = Bot::all();

        foreach ($bots as $bot) {
            $metadata = $bot->metadata ?? [];

            // Clean up RAG config - keep only essential fields
            if (isset($metadata['rag_config'])) {
                $ragConfig = $metadata['rag_config'];
                
                // Keep only essential RAG fields
                $cleanRagConfig = [
                    'enabled' => $ragConfig['enabled'] ?? false,
                    'auto_query' => $ragConfig['auto_query'] ?? false,
                    'collection' => $ragConfig['collection'] ?? 'documents',
                    'min_question_length' => $ragConfig['min_question_length'] ?? 10,
                    'top_k' => $ragConfig['top_k'] ?? 5,
                ];
                
                $metadata['rag_config'] = $cleanRagConfig;
            }

            // Remove parameter_limits completely (redundant with ModelService)
            unset($metadata['parameter_limits']);

            $bot->update(['metadata' => $metadata]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore previous RAG config structure
        $bots = Bot::all();

        foreach ($bots as $bot) {
            $metadata = $bot->metadata ?? [];

            // Restore full RAG config structure
            if (isset($metadata['rag_config'])) {
                $ragConfig = $metadata['rag_config'];
                
                $fullRagConfig = [
                    'enabled' => $ragConfig['enabled'] ?? false,
                    'auto_query' => $ragConfig['auto_query'] ?? false,
                    'top_k' => $ragConfig['top_k'] ?? 5,
                    'collection' => $ragConfig['collection'] ?? 'documents',
                    'allow_user_file_selection' => false,
                    'max_tokens_limit' => 2000,
                    'max_temperature' => 1.0,
                    'min_question_length' => $ragConfig['min_question_length'] ?? 10,
                ];
                
                $metadata['rag_config'] = $fullRagConfig;
            }

            // Restore parameter_limits
            if (!isset($metadata['parameter_limits'])) {
                $metadata['parameter_limits'] = [
                    'max_temperature' => 1.5,
                    'max_tokens_limit' => 4000,
                    'max_top_k' => 10,
                ];
            }

            $bot->update(['metadata' => $metadata]);
        }
    }
};

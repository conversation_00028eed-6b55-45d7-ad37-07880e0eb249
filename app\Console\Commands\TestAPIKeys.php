<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\ModelAI\Models\ModelProvider;
use Modules\ModelAI\Models\ModelAI;
use Modules\ChatBot\Services\AIService;
use Illuminate\Support\Facades\Log;
use Exception;

class TestAPIKeys extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:api-keys
                            {--provider= : Test specific provider (google, openai, etc.)}
                            {--model= : Test specific model key}
                            {--debug : Show detailed debug information}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test API keys for AI providers and debug connection issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔑 Testing AI Provider API Keys...');
        $this->newLine();

        $providerKey = $this->option('provider');
        $modelKey = $this->option('model');
        $verbose = $this->option('debug');

        if ($providerKey) {
            $this->testSpecificProvider($providerKey, $modelKey, $verbose);
        } else {
            $this->testAllProviders($verbose);
        }

        return 0;
    }

    /**
     * Test all active providers.
     */
    private function testAllProviders(bool $verbose): void
    {
        $providers = ModelProvider::active()->get();

        if ($providers->isEmpty()) {
            $this->warn('⚠️  No active providers found');
            return;
        }

        $this->info("Found {$providers->count()} active provider(s)");
        $this->newLine();

        foreach ($providers as $provider) {
            $this->testProvider($provider, null, $verbose);
            $this->newLine();
        }
    }

    /**
     * Test specific provider.
     */
    private function testSpecificProvider(string $providerKey, ?string $modelKey, bool $verbose): void
    {
        $provider = ModelProvider::where('key', $providerKey)->first();

        if (!$provider) {
            $this->error("❌ Provider '{$providerKey}' not found");
            return;
        }

        $this->testProvider($provider, $modelKey, $verbose);
    }

    /**
     * Test a provider and its models.
     */
    private function testProvider(ModelProvider $provider, ?string $specificModelKey, bool $verbose): void
    {
        $this->info("🔍 Testing Provider: {$provider->name} ({$provider->key})");

        // Test provider credentials
        $this->testProviderCredentials($provider, $verbose);

        // Test models
        $models = $provider->activeModelAIs();
        
        if ($specificModelKey) {
            $models = $models->where('key', $specificModelKey);
        }
        
        $models = $models->get();

        if ($models->isEmpty()) {
            $this->warn("   ⚠️  No active models found for this provider");
            return;
        }

        foreach ($models as $model) {
            $this->testModel($model, $verbose);
        }
    }

    /**
     * Test provider credentials.
     */
    private function testProviderCredentials(ModelProvider $provider, bool $verbose): void
    {
        // Test API key
        $apiKey = $provider->getApiKey();
        $hasApiKey = !empty($apiKey);
        
        $this->line("   📋 API Key: " . ($hasApiKey ? '✅ Present' : '❌ Missing'));
        
        if ($verbose && $hasApiKey) {
            $this->line("      - Length: " . strlen($apiKey));
            $this->line("      - Prefix: " . substr($apiKey, 0, 4) . '...');
            $this->line("      - Direct access: " . (!empty($provider->api_key) ? 'Yes' : 'No'));
        }

        // Test base URL
        if ($provider->base_url) {
            $this->line("   🌐 Base URL: ✅ {$provider->base_url}");
        } else {
            $this->line("   🌐 Base URL: ⚠️  Not set");
        }

        // Test credentials validation
        $hasValidCredentials = $provider->hasValidCredentials();
        $this->line("   🔐 Valid Credentials: " . ($hasValidCredentials ? '✅ Yes' : '❌ No'));

        // Test provider status
        $this->line("   📊 Status: " . ($provider->isActive() ? '✅ Active' : '❌ Inactive'));

        if ($verbose) {
            $credentials = $provider->getAllCredentials();
            if (!empty($credentials)) {
                $this->line("   🔑 Additional Credentials:");
                foreach ($credentials as $key => $value) {
                    $displayValue = $value ? (strlen($value) > 10 ? substr($value, 0, 4) . '...' : $value) : 'null';
                    $this->line("      - {$key}: {$displayValue}");
                }
            }
        }
    }

    /**
     * Test individual model.
     */
    private function testModel(ModelAI $model, bool $verbose): void
    {
        $this->line("   🤖 Model: {$model->name} ({$model->key})");

        try {
            // Test basic model properties
            $this->line("      📊 Status: " . ($model->status === 'active' ? '✅ Active' : '❌ Inactive'));
            $this->line("      🎯 Default: " . ($model->is_default ? '✅ Yes' : '❌ No'));

            // Test AI service call
            $this->testModelAICall($model, $verbose);

        } catch (Exception $e) {
            $this->line("      ❌ Error: " . $e->getMessage());
            
            if ($verbose) {
                $this->line("      🔍 Trace: " . $e->getTraceAsString());
            }
        }
    }

    /**
     * Test AI service call for model.
     */
    private function testModelAICall(ModelAI $model, bool $verbose): void
    {
        $aiService = new AIService();
        
        // Enable debug logging temporarily if verbose
        if ($verbose) {
            $originalLogLevel = config('logging.channels.single.level');
            config(['logging.channels.single.level' => 'debug']);
        }

        try {
            // Test with a simple prompt
            $testPrompt = "Hello, this is a test. Please respond with 'Test successful'.";
            
            $this->line("      🧪 Testing API call...");
            
            // Use reflection to call private method for testing
            $reflection = new \ReflectionClass($aiService);
            $method = $reflection->getMethod('callAIWithModel');
            $method->setAccessible(true);
            
            $result = $method->invoke($aiService, $testPrompt, $model);
            
            if ($result) {
                $this->line("      ✅ API Call: Success");
                if ($verbose) {
                    $this->line("      📝 Response: " . substr($result, 0, 100) . (strlen($result) > 100 ? '...' : ''));
                }
            } else {
                $this->line("      ❌ API Call: Failed (null response)");
            }

        } catch (Exception $e) {
            $this->line("      ❌ API Call: Failed - " . $e->getMessage());
            
            if ($verbose) {
                $this->line("      🔍 Error details: " . $e->getTraceAsString());
            }
        } finally {
            // Restore original log level
            if ($verbose && isset($originalLogLevel)) {
                config(['logging.channels.single.level' => $originalLogLevel]);
            }
        }
    }
}

<?php

namespace Modules\Location\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;

class BulkGeoDivisionRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ids' => [
                'required',
                'array',
                'min:1',
            ],
            'ids.*' => [
                'required',
                'integer',
                'exists:geo_divisions,id',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'ids' => __('Geographic Division IDs'),
            'ids.*' => __('Geographic Division ID'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ids.required' => __('Please select at least one geographic division.'),
            'ids.array' => __('Invalid data format.'),
            'ids.min' => __('Please select at least one geographic division.'),
            'ids.*.required' => __('Geographic Division ID is required.'),
            'ids.*.integer' => __('Geographic Division ID must be an integer.'),
            'ids.*.exists' => __('One or more selected geographic divisions do not exist.'),
        ];
    }
}

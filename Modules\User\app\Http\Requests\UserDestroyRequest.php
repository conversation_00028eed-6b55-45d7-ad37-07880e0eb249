<?php

namespace Modules\User\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

class UserDestroyRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'id' => [
                'required',
                'integer',
                Rule::exists('users', 'id')->whereNotNull('deleted_at'), // Only soft deleted users
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'id' => __('User ID'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'id.required' => __('User ID is required.'),
            'id.integer' => __('User ID must be an integer.'),
            'id.exists' => __('The selected user must be in trash to be permanently deleted.'),
        ];
    }
}

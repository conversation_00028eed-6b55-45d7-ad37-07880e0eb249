<?php

namespace Modules\ActiveCode\Models;

use Illuminate\Database\Eloquent\Model;

class ActiveCode extends Model
{
    protected $fillable = [
        'code', 'type', 'identifier', 'expires_at',
        'used_at', 'attempts'
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'used_at' => 'datetime'
    ];

    // Scopes
    public function scopeValid($query)
    {
        return $query->where('expires_at', '>', now())
                    ->whereNull('used_at');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByIdentifier($query, $identifier)
    {
        return $query->where('identifier', $identifier);
    }

    // Methods
    public function isExpired()
    {
        return $this->expires_at < now();
    }

    public function isValid()
    {
        return is_null($this->used_at) && !$this->isExpired();
    }

    public function markAsUsed()
    {
        $this->update(['used_at' => now()]);
    }
}

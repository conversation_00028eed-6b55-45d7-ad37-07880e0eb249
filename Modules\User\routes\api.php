<?php

use Illuminate\Support\Facades\Route;
use Modules\User\Http\Controllers\UserController;
use Modules\User\Http\Controllers\Auth\UserController as AuthUserController;
use Modules\User\Http\Controllers\Api\UserPublicController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('v1')->name('api.')->group(function () {
    // Public user endpoints
    Route::get('members/{username}', [UserController::class, 'show'])->name('users.show');

    // Public user endpoints by UUID
    Route::prefix('users/public')->name('users.public.')->group(function () {
        Route::get('{uuid}', [UserPublicController::class, 'show'])->name('show');
        Route::get('{uuid}/exists', [UserPublicController::class, 'exists'])->name('exists');
        Route::get('{uuid}/info', [UserPublicController::class, 'publicInfo'])->name('info');
    });
});

Route::middleware(['auth:api'])->prefix('v1/auth')->name('auth.')->group(function () {
    // Users dropdown
    Route::get('users/dropdown', [AuthUserController::class, 'dropdown'])->name('users.dropdown');

    // Standard CRUD routes
    Route::get('users', [AuthUserController::class, 'index'])->name('users.index');
    Route::post('users', [AuthUserController::class, 'store'])->name('users.store');
    Route::get('users/{id}', [AuthUserController::class, 'show'])->name('users.show');
    Route::put('users/{id}', [AuthUserController::class, 'update'])->name('users.update');
    Route::patch('users/{id}', [AuthUserController::class, 'update'])->name('users.update');

    // Custom delete operations
    Route::delete('users/{id}/delete', [AuthUserController::class, 'delete'])->name('users.delete');
    Route::delete('users/{id}/destroy', [AuthUserController::class, 'destroy'])->name('users.destroy');

    // Restore operations
    Route::put('users/{id}/restore', [AuthUserController::class, 'restore'])->name('users.restore');

    // Bulk operations
    Route::delete('users/bulk/delete', [AuthUserController::class, 'bulkDelete'])->name('users.bulk-delete');
    Route::delete('users/bulk/destroy', [AuthUserController::class, 'bulkDestroy'])->name('users.bulk-destroy');
    Route::put('users/bulk/restore', [AuthUserController::class, 'bulkRestore'])->name('users.bulk-restore');
});

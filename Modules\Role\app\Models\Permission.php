<?php

namespace Modules\Role\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Permission\Models\Permission as Model;
use Modules\Role\Database\Factories\PermissionFactory;
use Spatie\Permission\Traits\RefreshesPermissionCache;

class Permission extends Model
{
    use HasFactory, RefreshesPermissionCache;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'display_name',
        'guard_name',
        'description',
        'module_name',
        'sort_order'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $hidden = ['pivot'];

    /**
     * Scope a query to filter by module.
     */
    public function scopeForModule($query, string $module)
    {
        return $query->where('module_name', $module);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrderBySortOrder($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get permissions grouped by module.
     */
    public static function getByModule(): array
    {
        return static::orderBySortOrder()
            ->get()
            ->groupBy('module_name')
            ->toArray();
    }

    /**
     * Try to extract module from permission name using various patterns.
     */
    public function getInferredModule(): ?string
    {
        // If module_name is set, use it
        if ($this->module_name) {
            return $this->module_name;
        }

        // Try to infer from name using common patterns
        $name = $this->name;

        // Pattern: module.action
        if (str_contains($name, '.')) {
            return explode('.', $name)[0];
        }

        // Pattern: module_action
        if (str_contains($name, '_')) {
            return explode('_', $name)[0];
        }

        // Pattern: module-action
        if (str_contains($name, '-')) {
            return explode('-', $name)[0];
        }

        // Pattern: module::action
        if (str_contains($name, '::')) {
            return explode('::', $name)[0];
        }

        // Pattern: action_module (reverse)
        $commonActions = ['view', 'create', 'edit', 'delete', 'manage', 'list', 'show', 'store', 'update', 'destroy'];
        foreach ($commonActions as $action) {
            if (str_starts_with($name, $action . '_')) {
                return substr($name, strlen($action . '_'));
            }
        }

        // Cannot infer, return null
        return null;
    }

    /**
     * Try to extract action from permission name using various patterns.
     */
    public function getInferredAction(): ?string
    {
        $name = $this->name;

        // Pattern: module.action
        if (str_contains($name, '.')) {
            $parts = explode('.', $name);
            return end($parts);
        }

        // Pattern: module_action
        if (str_contains($name, '_')) {
            $parts = explode('_', $name);
            return end($parts);
        }

        // Pattern: module-action
        if (str_contains($name, '-')) {
            $parts = explode('-', $name);
            return end($parts);
        }

        // Pattern: module::action
        if (str_contains($name, '::')) {
            $parts = explode('::', $name);
            return end($parts);
        }

        // Pattern: action_module (reverse)
        $commonActions = ['view', 'create', 'edit', 'delete', 'manage', 'list', 'show', 'store', 'update', 'destroy'];
        foreach ($commonActions as $action) {
            if (str_starts_with($name, $action . '_')) {
                return $action;
            }
        }

        // Cannot infer, return the whole name
        return $name;
    }

    /**
     * Check if permission name follows a recognizable pattern.
     */
    public function hasRecognizablePattern(): bool
    {
        $name = $this->name;

        return str_contains($name, '.') ||
               str_contains($name, '_') ||
               str_contains($name, '-') ||
               str_contains($name, '::');
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): PermissionFactory
    {
        return PermissionFactory::new();
    }
}

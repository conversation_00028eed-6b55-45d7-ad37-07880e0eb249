<?php

namespace Modules\Auth\Http\Requests;

use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\User\Enums\UserGender;

class ProfileRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'first_name' => [
                'sometimes',
                'string',
                'max:100',
            ],
            'last_name' => [
                'sometimes',
                'string',
                'max:100',
            ],
            'avatar' => [
                'nullable',
                'string',
                'max:500',
            ],
            'birthday' => [
                'nullable',
                'date',
                'before:today',
            ],
            'gender' => [
                'sometimes',
                'string',
                Rule::in(UserGender::values()),
            ],
            'phone' => [
                'nullable',
                'string',
                'min:7',
                'max:15',
                'regex:/^(\+\d{1,3}|0)[0-9\s\-\(\)]{6,12}$/',
                Rule::unique('users')->ignore(Auth::id())->whereNull('deleted_at'),
            ],
            'address' => [
                'nullable',
                'string',
                'max:1000',
            ],
            'geo_division_id' => [
                'nullable',
                'integer',
                'exists:geo_divisions,id',
            ],
            'country_id' => [
                'nullable',
                'integer',
                'exists:countries,id',
            ],
            'newsletter_subscribed' => [
                'sometimes',
                'boolean',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'username.unique' => __('Username is already taken.'),
            'username.regex' => __('Username must start with a letter and contain only letters, numbers, hyphens, and underscores.'),
            'birthday.before' => __('Birthday must be before today.'),
            'gender.in' => __('Please select a valid gender.'),
            'phone.unique' => __('Phone number is already registered.'),
            'phone.regex' => __('Please enter a valid phone number.'),
            'geo_division_id.exists' => __('Selected geographic division is invalid.'),
            'country_id.exists' => __('Selected country is invalid.'),
        ];
    }
}

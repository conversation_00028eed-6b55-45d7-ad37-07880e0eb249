<?php

namespace Modules\Auth\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Auth\Services\AuthRateLimitService;
use Modules\Core\Traits\ResponseTrait;

/**
 * AuthRateLimitMiddleware
 * 
 * Middleware to apply rate limiting to authentication endpoints.
 * Uses AuthRateLimitService for flexible, settings-based rate limiting.
 */
class AuthRateLimitMiddleware
{
    use ResponseTrait;

    protected AuthRateLimitService $rateLimitService;

    public function __construct(AuthRateLimitService $rateLimitService)
    {
        $this->rateLimitService = $rateLimitService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $action = 'default'): mixed
    {
        // Check if account is locked out (for login attempts)
        if ($action === 'login') {
            $identifier = $this->rateLimitService->getIdentifier($request, $action);
            if ($this->rateLimitService->isLockedOut($identifier)) {
                $lockoutTime = $this->rateLimitService->getLockoutTimeRemaining($identifier);
                
                return $this->errorResponse(
                    [
                        'lockout_time_remaining' => $lockoutTime,
                        'message_detail' => __('Account is temporarily locked. Please try again in :minutes minutes.', [
                            'minutes' => ceil($lockoutTime / 60)
                        ])
                    ],
                    __('Account temporarily locked due to too many failed attempts.'),
                    423 // HTTP 423 Locked
                );
            }
        }

        // Check rate limiting
        if ($this->rateLimitService->isRateLimited($request, $action)) {
            $remainingAttempts = $this->rateLimitService->getRemainingAttempts($request, $action);
            $resetTime = $this->rateLimitService->getResetTime($request, $action);
            
            return $this->errorResponse(
                [
                    'remaining_attempts' => $remainingAttempts,
                    'reset_time' => $resetTime,
                    'retry_after' => $resetTime,
                    'message_detail' => __('Too many attempts. Please try again in :minutes minutes.', [
                        'minutes' => ceil($resetTime / 60)
                    ])
                ],
                __('Rate limit exceeded. Too many requests.'),
                429 // HTTP 429 Too Many Requests
            );
        }

        // Record the attempt before processing
        $this->rateLimitService->recordAttempt($request, $action, false);

        $response = $next($request);

        // If request was successful (2xx status), record as successful
        if ($response instanceof JsonResponse) {
            $statusCode = $response->getStatusCode();
            if ($statusCode >= 200 && $statusCode < 300) {
                $this->rateLimitService->recordAttempt($request, $action, true);
            }
        }

        // Add rate limit headers to response
        $this->addRateLimitHeaders($response, $request, $action);

        return $response;
    }

    /**
     * Add rate limit headers to response.
     */
    protected function addRateLimitHeaders($response, Request $request, string $action): void
    {
        $maxAttempts = $this->rateLimitService->getMaxAttempts($action);
        $remainingAttempts = $this->rateLimitService->getRemainingAttempts($request, $action);
        $resetTime = $this->rateLimitService->getResetTime($request, $action);

        if (method_exists($response, 'header')) {
            $response->header('X-RateLimit-Limit', $maxAttempts);
            $response->header('X-RateLimit-Remaining', max(0, $remainingAttempts));
            
            if ($resetTime) {
                $response->header('X-RateLimit-Reset', now()->addSeconds($resetTime)->timestamp);
                $response->header('Retry-After', $resetTime);
            }
        }
    }
}

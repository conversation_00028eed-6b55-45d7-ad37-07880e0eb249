<?php

namespace Modules\ChatBot\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;

class KnowledgeBaseRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'name' => 'required|string|max:255',
        ];

        // Add specific rules based on request method
        if ($this->isMethod('post')) {
            // For create operations
            $rules['content'] = 'required_without:storage_path|string|max:1000000'; // 1MB text limit
            $rules['storage_path'] = 'required_without:content|string';
        } elseif ($this->isMethod('put') || $this->isMethod('patch')) {
            // For update operations
            $rules['name'] = 'sometimes|required|string|max:255';
            $rules['content'] = 'sometimes|string|max:1000000';
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Knowledge base name is required.',
            'name.string' => 'Knowledge base name must be a string.',
            'name.max' => 'Knowledge base name cannot exceed 255 characters.',
            'content.required_without' => 'Content is required when storage path is not provided.',
            'content.string' => 'Content must be a string.',
            'content.max' => 'Content cannot exceed 1MB.',
            'storage_path.required_without' => 'Storage path is required when content is not provided.',
            'storage_path.string' => 'Storage path must be a string.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => 'knowledge base name',
            'content' => 'content',
            'storage_path' => 'storage path',
        ];
    }
}

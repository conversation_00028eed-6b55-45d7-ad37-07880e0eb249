<?php

namespace Modules\Blog\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Modules\Blog\Facades\BlogFacade;
use Modules\Blog\Http\Filters\BlogCategoryFilter;
use Modules\Blog\Http\Requests\BlogCategoryRequest;
use Modules\Blog\Http\Requests\BulkBlogCategoryRequest;
use Modules\Blog\Http\Requests\BulkBlogCategoryDestroyRequest;
use Modules\Blog\Models\BlogCategory;
use Modules\Core\Traits\ResponseTrait;
use Throwable;

class BlogCategoryController extends Controller
{
    use ResponseTrait;


    public function __construct()
    {
        $this->middleware('role_or_permission:super-admin|blog.category.view')->only(['index', 'show', 'edit', 'dropdown']);
        $this->middleware('role_or_permission:super-admin|blog.category.create')->only(['store']);
        $this->middleware('role_or_permission:super-admin|blog.category.edit')->only(['update']);
        $this->middleware('role_or_permission:super-admin|blog.category.delete')->only(['delete', 'restore', 'bulkDelete', 'bulkRestore']);
        $this->middleware('role_or_permission:super-admin|blog.category.destroy')->only(['destroy', 'bulkDestroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $categories = BlogCategory::query()
            ->with(['parent', 'children', 'translations'])
            ->filter(new BlogCategoryFilter($request))
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($categories, __('Languages retrieved successfully.'));
    }

    /**
     * Store a newly created resource in storage.
     * @throws Throwable
     */
    public function store(BlogCategoryRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $category = BlogCategory::create($request->all());

            if ($request->has('translations')) {
                foreach ($request->input('translations') as $locale => $translation) {
                    $category->translateOrNew($locale)->fill($translation)->save();
                }
            }

            DB::commit();
            return $this->successResponse($category, __('Blog category created successfully.'), 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse($request->validated(), __('Blog category creation failed.'), 422);
        }
    }

    /**
     * Show the specified resource.
     */
    public function show(Request $request, BlogCategory $category): JsonResponse
    {
        $category->load(['translations', 'parent', 'children']);
        return $this->successResponse($category, __('Blog category retrieved successfully.'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BlogCategory $category): JsonResponse
    {
        $category->load(['translations', 'parent', 'children']);
        return $this->successResponse($category, __('Blog category retrieved successfully.'));
    }

    /**
     * Update the specified resource in storage.
     * @throws Throwable
     */
    public function update(BlogCategoryRequest $request, BlogCategory $category): JsonResponse
    {
        try {
            DB::beginTransaction();

            $category->update($request->all());

            if ($request->has('translations')) {
                foreach ($request->input('translations') as $locale => $translation) {
                    $category->translateOrNew($locale)->fill($translation)->save();
                }
            }

            DB::commit();
            return $this->successResponse($category->fresh(), __('Blog category updated successfully!'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse(null, __('Blog category update failed!'), 422);
        }
    }

    /**
     * Soft delete the specified resource from storage.
     */
    public function delete(int $id): JsonResponse
    {
        $category = BlogCategory::findOrFail($id);
        $category->delete();
        return $this->successResponse(null, __('Blog category moved to trash successfully!'));
    }

    public function restore(int $id): JsonResponse
    {
        $category = BlogCategory::onlyTrashed()->findOrFail($id);
        $category->restore();

        return $this->successResponse($category, __('Blog category restored successfully.'));
    }

    public function destroy(int $id): JsonResponse
    {
        $category = BlogCategory::onlyTrashed()->findOrFail($id);
        $category->forceDelete();

        return $this->successResponse($id, __('Blog category permanently deleted.'));
    }

    /**
     * Bulk soft delete categories.
     */
    public function bulkDelete(BulkBlogCategoryRequest $request): JsonResponse
    {
        $deletedCount = BlogCategory::whereIn('id', $request->validated('ids'))->delete();
        return $this->successResponse(
            ['deleted_count' => $deletedCount],
            __(':count blog categories moved to trash.', ['count' => $deletedCount])
        );
    }

    /**
     * Bulk restore categories.
     */
    public function bulkRestore(BulkBlogCategoryRequest $request): JsonResponse
    {
        $restoredCount = BlogCategory::onlyTrashed()
            ->whereIn('id', $request->validated('ids'))
            ->restore();

        return $this->successResponse(
            ['restored_count' => $restoredCount],
            __(':count blog categories restored successfully.', ['count' => $restoredCount])
        );
    }

    /**
     * Bulk permanently delete categories.
     */
    public function bulkDestroy(BulkBlogCategoryDestroyRequest $request): JsonResponse
    {
        $deletedCount = BlogCategory::onlyTrashed()
            ->whereIn('id', $request->validated('ids'))
            ->forceDelete();

        return $this->successResponse(
            ['deleted_count' => $deletedCount],
            __(':count blog categories permanently deleted.', ['count' => $deletedCount])
        );
    }

    /**
     * Get categories dropdown for admin.
     */
    public function dropdown(Request $request): JsonResponse
    {
        $categories = BlogFacade::getActiveCategoryHierarchy(currentLocale());
        return $this->successResponse($categories, __('Categories dropdown retrieved successfully.'));
    }
}

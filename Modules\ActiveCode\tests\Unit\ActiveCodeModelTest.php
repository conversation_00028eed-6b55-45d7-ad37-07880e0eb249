<?php

namespace Modules\ActiveCode\Tests\Unit;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\ActiveCode\Models\ActiveCode;
use Modules\ActiveCode\Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

/**
 * Unit tests for ActiveCode Model
 *
 * Test all model scopes and methods
 */
class ActiveCodeModelTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function test_model_has_correct_fillable_attributes()
    {
        $expectedFillable = [
            'code', 'type', 'identifier', 'expires_at',
            'used_at', 'attempts',
        ];

        $model = new ActiveCode();
        $this->assertEquals($expectedFillable, $model->getFillable());
    }

    #[Test]
    public function test_model_has_correct_casts()
    {
        $expectedCasts = [
            'expires_at' => 'datetime',
            'used_at' => 'datetime',
            'id' => 'int',
        ];

        $model = new ActiveCode();
        $casts = $model->getCasts();

        foreach ($expectedCasts as $attribute => $cast) {
            $this->assertEquals($cast, $casts[$attribute]);
        }
    }

    #[Test]
    public function test_valid_scope_returns_only_unexpired_and_unused_codes()
    {
        // Create valid code
        $validCode = ActiveCode::factory()->valid()->create();

        // Create expired code
        $expiredCode = ActiveCode::factory()->expired()->create();

        // Create used code
        $usedCode = ActiveCode::factory()->used()->create();

        $validCodes = ActiveCode::valid()->get();

        $this->assertCount(1, $validCodes);
        $this->assertTrue($validCodes->contains($validCode));
        $this->assertFalse($validCodes->contains($expiredCode));
        $this->assertFalse($validCodes->contains($usedCode));
    }

    #[Test]
    public function test_byType_scope_filters_by_code_type()
    {
        $type = 'email_verification';

        // Create code with target type
        $emailCode = ActiveCode::factory()->ofType($type)->create();

        // Create code with different type
        $otherCode = ActiveCode::factory()->ofType('login_otp')->create();

        $codesByType = ActiveCode::byType($type)->get();

        $this->assertCount(1, $codesByType);
        $this->assertTrue($codesByType->contains($emailCode));
        $this->assertFalse($codesByType->contains($otherCode));
    }

    #[Test]
    public function test_byIdentifier_scope_filters_by_identifier()
    {
        $identifier = '<EMAIL>';

        // Create code with target identifier
        $targetCode = ActiveCode::factory()->forIdentifier($identifier)->create();

        // Create code with different identifier
        $otherCode = ActiveCode::factory()->forIdentifier('<EMAIL>')->create();

        $codesByIdentifier = ActiveCode::byIdentifier($identifier)->get();

        $this->assertCount(1, $codesByIdentifier);
        $this->assertTrue($codesByIdentifier->contains($targetCode));
        $this->assertFalse($codesByIdentifier->contains($otherCode));
    }

    #[Test]
    public function test_combining_multiple_scopes()
    {
        $type = 'verification';
        $identifier = '<EMAIL>';

        // Create code matching both type and identifier
        $matchingCode = ActiveCode::factory()->valid()->create([
            'type' => $type,
            'identifier' => $identifier
        ]);

        // Create code matching only type
        $typeOnlyCode = ActiveCode::factory()->valid()->create([
            'type' => $type,
            'identifier' => '<EMAIL>'
        ]);

        // Create code matching only identifier
        $identifierOnlyCode = ActiveCode::factory()->valid()->create([
            'type' => 'login_otp',
            'identifier' => $identifier
        ]);

        $codes = ActiveCode::valid()
            ->byType($type)
            ->byIdentifier($identifier)
            ->get();

        $this->assertCount(1, $codes);
        $this->assertTrue($codes->contains($matchingCode));
        $this->assertFalse($codes->contains($typeOnlyCode));
        $this->assertFalse($codes->contains($identifierOnlyCode));
    }

    #[Test]
    public function test_isExpired_method_returns_true_when_code_expired()
    {
        $expiredCode = ActiveCode::factory()->expired()->create();

        $this->assertTrue($expiredCode->isExpired());
    }

    #[Test]
    public function test_isExpired_method_returns_false_when_code_not_expired()
    {
        $validCode = ActiveCode::factory()->valid()->create();

        $this->assertFalse($validCode->isExpired());
    }

    #[Test]
    public function test_isValid_method_returns_true_when_code_unused_and_not_expired()
    {
        $validCode = ActiveCode::factory()->valid()->create();

        $this->assertTrue($validCode->isValid());
    }

    #[Test]
    public function test_isValid_method_returns_false_when_code_used()
    {
        $usedCode = ActiveCode::factory()->used()->create();

        $this->assertFalse($usedCode->isValid());
    }

    #[Test]
    public function test_isValid_method_returns_false_when_code_expired()
    {
        $expiredCode = ActiveCode::factory()->expired()->create();

        $this->assertFalse($expiredCode->isValid());
    }

    #[Test]
    public function test_isValid_method_returns_false_when_code_both_expired_and_used()
    {
        $code = ActiveCode::factory()->create([
            'expires_at' => now()->subHour(),
            'used_at' => now()->subMinutes(30)
        ]);

        $this->assertFalse($code->isValid());
    }

    #[Test]
    public function test_markAsUsed_method_updates_used_at()
    {
        $code = ActiveCode::factory()->valid()->create();

        $this->assertNull($code->used_at);

        $code->markAsUsed();

        $this->assertNotNull($code->used_at);
        $this->assertTrue($code->used_at->isToday());

        // Check in database
        $this->assertDatabaseHas('active_codes', [
            'id' => $code->id,
            'used_at' => $code->used_at
        ]);
    }

    #[Test]
    public function test_markAsUsed_method_can_be_called_multiple_times()
    {
        $code = ActiveCode::factory()->valid()->create();

        $code->markAsUsed();
        $firstUsedAt = $code->used_at;

        // Wait a bit for different time
        sleep(1);

        $code->markAsUsed();
        $secondUsedAt = $code->fresh()->used_at;

        // used_at will be updated again
        $this->assertNotEquals($firstUsedAt, $secondUsedAt);
    }

    #[Test]
    public function test_factory_creates_code_with_correct_default_attributes()
    {
        $code = ActiveCode::factory()->create();

        $this->assertNotNull($code->code);
        $this->assertNotNull($code->type);
        $this->assertNotNull($code->identifier);
        $this->assertNotNull($code->expires_at);
        $this->assertNull($code->used_at);
        $this->assertIsInt($code->attempts);
        $this->assertGreaterThanOrEqual(0, $code->attempts);
    }

    #[Test]
    public function test_factory_creates_codes_with_different_types()
    {
        $types = [
            'verification',
            'registration_verification',
            'login_otp',
            'email_verification',
            'phone_verification',
            'password_reset',
            'two_factor_auth',
            'account_activation',
            'security_verification'
        ];

        foreach ($types as $type) {
            $code = ActiveCode::factory()->ofType($type)->create();
            $this->assertEquals($type, $code->type);
        }
    }

    #[Test]
    public function test_factory_creates_codes_with_different_states()
    {
        // Test valid code
        $validCode = ActiveCode::factory()->valid()->create();
        $this->assertTrue($validCode->isValid());

        // Test expired code
        $expiredCode = ActiveCode::factory()->expired()->create();
        $this->assertTrue($expiredCode->isExpired());

        // Test used code
        $usedCode = ActiveCode::factory()->used()->create();
        $this->assertNotNull($usedCode->used_at);
        $this->assertFalse($usedCode->isValid());
    }

    #[Test]
    public function test_factory_creates_codes_with_specific_identifier()
    {
        $email = '<EMAIL>';
        $phone = '+***********';

        $emailCode = ActiveCode::factory()->forEmail($email)->create();
        $phoneCode = ActiveCode::factory()->forPhone($phone)->create();

        $this->assertEquals($email, $emailCode->identifier);
        $this->assertEquals($phone, $phoneCode->identifier);
    }

    #[Test]
    public function test_factory_creates_codes_with_specific_attempts()
    {
        $attempts = 3;
        $code = ActiveCode::factory()->withAttempts($attempts)->create();

        $this->assertEquals($attempts, $code->attempts);
    }
}

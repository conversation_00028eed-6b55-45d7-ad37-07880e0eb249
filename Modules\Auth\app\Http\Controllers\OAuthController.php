<?php

namespace Modules\Auth\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Core\Traits\ResponseTrait;
use Modules\Auth\Facades\AuthFacade;

class OAuthController extends Controller
{
    use ResponseTrait;

    /**
     * Get user's linked OAuth accounts.
     */
    public function getLinkedAccounts(): JsonResponse
    {
        $accounts = AuthFacade::getLinkedAccounts();
        
        return $this->successResponse(
            $accounts,
            __('Linked OAuth accounts retrieved successfully.')
        );
    }

    /**
     * Link OAuth provider to current user.
     */
    public function linkProvider(Request $request, string $provider): JsonResponse
    {
        $request->validate([
            'provider' => 'required|string|in:google,facebook,twitter,apple,telegram,zalo'
        ]);

        $result = AuthFacade::linkProvider($provider);
        
        if ($result['success']) {
            return $this->successResponse(
                ['redirect_url' => $result['redirect_url']],
                __('Redirecting to :provider for linking...', ['provider' => ucfirst($provider)])
            );
        }
        
        return $this->errorResponse(
            null,
            $result['message'],
            400
        );
    }

    /**
     * Unlink OAuth provider from current user.
     */
    public function unlinkProvider(string $provider): JsonResponse
    {
        $result = AuthFacade::unlinkProvider($provider);
        
        if ($result['success']) {
            return $this->successResponse(
                null,
                $result['message']
            );
        }
        
        return $this->errorResponse(
            null,
            $result['message'],
            400
        );
    }

    /**
     * Get available OAuth providers for linking.
     */
    public function getAvailableProviders(): JsonResponse
    {
        $user = auth()->user();
        $enabledProviders = AuthFacade::getEnabledProviders();
        $linkedProviders = $user ? $user->getLinkedProviders() : [];
        
        // Filter out already linked providers
        $availableProviders = array_filter($enabledProviders, function ($provider) use ($linkedProviders) {
            return !in_array($provider['name'], $linkedProviders);
        });
        
        return $this->successResponse(
            array_values($availableProviders),
            __('Available OAuth providers retrieved successfully.')
        );
    }
}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\Organization\Models\Organization;
use Modules\User\Models\User;
use Modules\Organization\Http\Requests\Organization\DestroyOrganizationRequest;

class TestOrganizationDelete extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:organization-delete {user_id} {organization_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test organization deletion for a user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id');
        $organizationId = $this->argument('organization_id');
        
        $user = User::find($userId);
        $organization = Organization::find($organizationId);
        
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return;
        }
        
        if (!$organization) {
            $this->error("Organization with ID {$organizationId} not found");
            return;
        }

        $this->info("Testing organization deletion:");
        $this->info("User: {$user->email} (ID: {$user->id})");
        $this->info("Organization: {$organization->name} (ID: {$organization->id})");
        $this->info("Organization Owner ID: {$organization->owner_id}");
        
        try {
            // Test policy directly
            $canDelete = $user->can('delete', $organization);
            $this->info("Policy allows delete: " . ($canDelete ? 'YES' : 'NO'));
            
            // Test request authorization
            $request = new DestroyOrganizationRequest();
            $request->setUserResolver(function() use ($user) {
                return $user;
            });
            
            // Mock route parameter
            $request->setRouteResolver(function() use ($organization) {
                return new class($organization) {
                    private $organization;
                    public function __construct($organization) {
                        $this->organization = $organization;
                    }
                    public function parameter($key) {
                        if ($key === 'organization' || $key === 'space') {
                            return $this->organization;
                        }
                        return null;
                    }
                };
            });
            
            $canAuthorize = $request->authorize();
            $this->info("Request authorize: " . ($canAuthorize ? 'YES' : 'NO'));
            
            if ($canAuthorize) {
                $this->info("✓ User can delete this organization");
                
                // Ask for confirmation before actually deleting
                if ($this->confirm('Do you want to actually delete this organization?')) {
                    $organization->delete();
                    $this->info("✓ Organization deleted successfully");
                } else {
                    $this->info("Deletion cancelled");
                }
            } else {
                $this->error("✗ User cannot delete this organization");
                
                // Check specific reasons
                if ($user->id !== $organization->owner_id) {
                    $this->error("Reason: User is not the owner of this organization");
                }
            }
            
        } catch (\Exception $e) {
            $this->error("✗ Failed to test deletion:");
            $this->error($e->getMessage());
            $this->error("File: {$e->getFile()}:{$e->getLine()}");
        }
    }
}

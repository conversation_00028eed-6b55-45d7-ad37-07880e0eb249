<?php

namespace Modules\Core\Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class CorePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedCorePermissions();
        $this->assignPermissionsToSuperAdmin();
    }

    /**
     * Seed core module permissions.
     */
    private function seedCorePermissions(): void
    {
        $permissions = [
            [
                'name' => 'core.view',
                'display_name' => 'View Core',
                'description' => 'Permission to view core system information',
                'module_name' => 'core',
                'sort_order' => 1,
                'guard_name' => 'api',
            ],
            [
                'name' => 'core.create',
                'display_name' => 'Create Core',
                'description' => 'Permission to create core system resources',
                'module_name' => 'core',
                'sort_order' => 2,
                'guard_name' => 'api',
            ],
            [
                'name' => 'core.edit',
                'display_name' => 'Edit Core',
                'description' => 'Permission to update core system resources',
                'module_name' => 'core',
                'sort_order' => 3,
                'guard_name' => 'api',
            ],
            [
                'name' => 'core.delete',
                'display_name' => 'Soft Delete Core',
                'description' => 'Permission to soft delete core system resources',
                'module_name' => 'core',
                'sort_order' => 4,
                'guard_name' => 'api',
            ],
            [
                'name' => 'core.destroy',
                'display_name' => 'Force Delete Core',
                'description' => 'Permission to permanently delete core system resources',
                'module_name' => 'core',
                'sort_order' => 5,
                'guard_name' => 'api',
            ],
            [
                'name' => 'core.restore',
                'display_name' => 'Restore Core',
                'description' => 'Permission to restore deleted core system resources',
                'module_name' => 'core',
                'sort_order' => 6,
                'guard_name' => 'api',
            ],
            [
                'name' => 'core.system.manage',
                'display_name' => 'Manage Core System',
                'description' => 'Permission to manage core system settings and configuration',
                'module_name' => 'core',
                'sort_order' => 7,
                'guard_name' => 'api',
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => 'api'],
                array_merge($permission, ['guard_name' => 'api'])
            );
        }
    }

    /**
     * Assign core permissions to super-admin role.
     */
    private function assignPermissionsToSuperAdmin(): void
    {
        $superAdmin = Role::where('name', 'super-admin')->where('guard_name', 'api')->first();
        
        if ($superAdmin) {
            $corePermissions = Permission::where('module_name', 'core')->where('guard_name', 'api')->get();
            
            foreach ($corePermissions as $permission) {
                if (!$superAdmin->hasPermissionTo($permission->name)) {
                    $superAdmin->givePermissionTo($permission->name);
                }
            }
        }
    }
}

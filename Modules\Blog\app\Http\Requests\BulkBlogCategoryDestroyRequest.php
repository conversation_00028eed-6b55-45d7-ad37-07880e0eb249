<?php

namespace Modules\Blog\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

class BulkBlogCategoryDestroyRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ids' => [
                'required',
                'array',
                'min:1',
            ],
            'ids.*' => [
                'required',
                'integer',
                Rule::exists('blog_categories', 'id')->whereNotNull('deleted_at'), // Only soft deleted categories
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'ids' => __('Blog Category IDs'),
            'ids.*' => __('Blog Category ID'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ids.required' => __('Please select at least one blog category.'),
            'ids.array' => __('Invalid data format.'),
            'ids.min' => __('Please select at least one blog category.'),
            'ids.*.required' => __('Blog Category ID is required.'),
            'ids.*.integer' => __('Blog Category ID must be an integer.'),
            'ids.*.exists' => __('One or more selected blog categories must be in trash to be permanently deleted.'),
        ];
    }
}

<?php

namespace Modules\Page\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;

class BulkPageRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ids' => [
                'required',
                'array',
                'min:1',
            ],
            'ids.*' => [
                'required',
                'integer',
                'exists:pages,id',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'ids' => __('Page IDs'),
            'ids.*' => __('Page ID'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ids.required' => __('Please select at least one page.'),
            'ids.array' => __('Invalid data format.'),
            'ids.min' => __('Please select at least one page.'),
            'ids.*.required' => __('Page ID is required.'),
            'ids.*.integer' => __('Page ID must be an integer.'),
            'ids.*.exists' => __('One or more selected pages do not exist.'),
        ];
    }
}

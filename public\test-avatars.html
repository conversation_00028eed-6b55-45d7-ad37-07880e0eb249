<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bot Avatars Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .avatar-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .avatar-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            background: #fafafa;
            transition: transform 0.2s;
        }
        .avatar-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .avatar-image {
            width: 80px;
            height: 80px;
            margin: 0 auto 10px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .avatar-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .bot-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
            font-size: 14px;
        }
        .bot-id {
            color: #666;
            font-size: 12px;
            margin-bottom: 10px;
        }
        .avatar-url {
            font-size: 11px;
            color: #888;
            word-break: break-all;
            background: #f0f0f0;
            padding: 5px;
            border-radius: 4px;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
            color: #2e7d32;
        }
        .error {
            background: #ffebee;
            border-color: #f44336;
            color: #c62828;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Bot Avatars Test</h1>
        
        <div class="status">
            ✅ All bot avatars are automatically generated and accessible!<br>
            📁 Stored in: <code>storage/app/public/bot-avatars/</code><br>
            🌐 Accessible via: <code>/storage/bot-avatars/</code>
        </div>

        <div class="avatar-grid">
            <!-- Sample avatars for testing -->
            <div class="avatar-card">
                <div class="avatar-image">
                    <img src="/storage/bot-avatars/bot-10-avatar.svg" alt="Customer Support Assistant" onerror="this.style.display='none'">
                </div>
                <div class="bot-name">Customer Support Assistant</div>
                <div class="bot-id">Bot ID: 10</div>
                <div class="avatar-url">/storage/bot-avatars/bot-10-avatar.svg</div>
            </div>

            <div class="avatar-card">
                <div class="avatar-image">
                    <img src="/storage/bot-avatars/bot-11-avatar.svg" alt="Content Writer Bot" onerror="this.style.display='none'">
                </div>
                <div class="bot-name">Content Writer Bot</div>
                <div class="bot-id">Bot ID: 11</div>
                <div class="avatar-url">/storage/bot-avatars/bot-11-avatar.svg</div>
            </div>

            <div class="avatar-card">
                <div class="avatar-image">
                    <img src="/storage/bot-avatars/bot-12-avatar.svg" alt="Code Review Assistant" onerror="this.style.display='none'">
                </div>
                <div class="bot-name">Code Review Assistant</div>
                <div class="bot-id">Bot ID: 12</div>
                <div class="avatar-url">/storage/bot-avatars/bot-12-avatar.svg</div>
            </div>

            <div class="avatar-card">
                <div class="avatar-image">
                    <img src="/storage/bot-avatars/bot-13-avatar.svg" alt="Data Analysis Bot" onerror="this.style.display='none'">
                </div>
                <div class="bot-name">Data Analysis Bot</div>
                <div class="bot-id">Bot ID: 13</div>
                <div class="avatar-url">/storage/bot-avatars/bot-13-avatar.svg</div>
            </div>

            <div class="avatar-card">
                <div class="avatar-image">
                    <img src="/storage/bot-avatars/bot-14-avatar.svg" alt="Language Tutor" onerror="this.style.display='none'">
                </div>
                <div class="bot-name">Language Tutor</div>
                <div class="bot-id">Bot ID: 14</div>
                <div class="avatar-url">/storage/bot-avatars/bot-14-avatar.svg</div>
            </div>

            <div class="avatar-card">
                <div class="avatar-image">
                    <img src="/storage/bot-avatars/bot-15-avatar.svg" alt="Public Marketing Assistant" onerror="this.style.display='none'">
                </div>
                <div class="bot-name">Public Marketing Assistant</div>
                <div class="bot-id">Bot ID: 15</div>
                <div class="avatar-url">/storage/bot-avatars/bot-15-avatar.svg</div>
            </div>

            <div class="avatar-card">
                <div class="avatar-image">
                    <img src="/storage/bot-avatars/bot-16-avatar.svg" alt="Organization HR Bot" onerror="this.style.display='none'">
                </div>
                <div class="bot-name">Organization HR Bot</div>
                <div class="bot-id">Bot ID: 16</div>
                <div class="avatar-url">/storage/bot-avatars/bot-16-avatar.svg</div>
            </div>

            <div class="avatar-card">
                <div class="avatar-image">
                    <img src="/storage/bot-avatars/bot-31-avatar.svg" alt="Bot No KB" onerror="this.style.display='none'">
                </div>
                <div class="bot-name">Bot No KB</div>
                <div class="bot-id">Bot ID: 31</div>
                <div class="avatar-url">/storage/bot-avatars/bot-31-avatar.svg</div>
            </div>
        </div>

        <div style="margin-top: 30px; text-align: center; color: #666;">
            <p>🎨 Avatars are automatically generated with unique colors based on bot ID</p>
            <p>📝 Each avatar shows the first letter of the bot name</p>
            <p>🔄 New bots will automatically get avatars when created</p>
        </div>
    </div>

    <script>
        // Add error handling for images
        document.querySelectorAll('img').forEach(img => {
            img.addEventListener('error', function() {
                this.parentElement.innerHTML = '<div style="width:80px;height:80px;background:#ddd;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#999;">❌</div>';
            });
        });
    </script>
</body>
</html>

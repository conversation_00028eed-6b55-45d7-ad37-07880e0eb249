# User Module API Documentation

## Tổng quan

Module User cung cấp các API endpoints để quản lý người dùng trong hệ thống Laravel ProCMS. Module này hỗ trợ cả public APIs (không cần authentication) và admin APIs (cần authentication và permissions).

## Base URL
```
/api/v1
```

## Authentication
- **Public APIs**: Không cần authentication
- **Admin APIs**: Cần JWT token trong header `Authorization: Bearer {token}`

## Response Format

Tất cả APIs sử dụng ResponseTrait với format chuẩn:

### Success Response
```json
{
  "success": true,
  "message": "Success message",
  "data": { ... }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error message",
  "data": null
}
```

### Paginated Response
```json
{
  "success": true,
  "message": "Success message",
  "data": [...],
  "total": 100,
  "limit": 10
}
```

---

## Public User APIs

### 1. Get User by <PERSON>rna<PERSON> th<PERSON>ng tin user công khai theo username.

**Endpoint:** `GET /members/{username}`

**Parameters:**
- `username` (string, required): Username của user

**Response:**
```json
{
  "success": true,
  "message": "User retrieved successfully.",
  "data": {
    "uuid": "550e8400-e29b-41d4-a716-************",
    "username": "johndoe",
    "first_name": "John",
    "last_name": "Doe",
    "avatar": "storage/avatars/user.jpg",
    "birthday": "1990-01-15",
    "gender": "male",
    "status": "active",
    "is_verified": true,
    "newsletter_subscribed": false,
    "geo_division": {
      "id": 1,
      "name": "Ho Chi Minh City",
      "type": "city"
    },
    "country": {
      "id": 1,
      "name": "Vietnam",
      "code": "VN"
    },
    "created_at": "2024-01-01 10:00:00",
    "updated_at": "2024-01-15 14:30:00"
  }
}
```

**Hidden Fields (for security):**
- `id`, `email`, `phone`, `address`, `last_login_at`, `last_login_ip`, `preferences`, `email_verified_at`, `phone_verified_at`

### 2. Get User by UUID
Lấy thông tin user công khai theo UUID.

**Endpoint:** `GET /users/public/{uuid}`

**Parameters:**
- `uuid` (string, required): UUID của user

**Response:**
```json
{
  "success": true,
  "message": "User profile retrieved successfully",
  "data": {
    "uuid": "550e8400-e29b-41d4-a716-************",
    "username": "johndoe",
    "full_name": "John Doe",
    "first_name": "John",
    "last_name": "Doe",
    "avatar_url": "https://domain.com/storage/avatars/user.jpg",
    "gender": {
      "value": "male",
      "label": "Male",
      "icon": "fas fa-mars"
    },
    "status": {
      "value": "active",
      "label": "Active",
      "css_class": "success"
    },
    "is_verified": true,
    "newsletter_subscribed": false,
    "location": {
      "country": {
        "id": 1,
        "name": "Vietnam",
        "code": "VN"
      },
      "geo_division": {
        "id": 1,
        "name": "Ho Chi Minh City",
        "type": "city"
      }
    },
    "created_at": "2024-01-01 10:00:00",
    "updated_at": "2024-01-15 14:30:00"
  }
}
```

### 3. Check User Exists
Kiểm tra user có tồn tại theo UUID.

**Endpoint:** `GET /users/public/{uuid}/exists`

**Parameters:**
- `uuid` (string, required): UUID của user

**Response:**
```json
{
  "success": true,
  "message": "User existence checked successfully",
  "data": {
    "exists": true
  }
}
```

### 4. Get User Public Info
Lấy thông tin cơ bản của user theo UUID.

**Endpoint:** `GET /users/public/{uuid}/info`

**Parameters:**
- `uuid` (string, required): UUID của user

**Response:**
```json
{
  "success": true,
  "message": "User public information retrieved successfully",
  "data": {
    "uuid": "550e8400-e29b-41d4-a716-************",
    "full_name": "John Doe",
    "avatar_url": "https://domain.com/storage/avatars/user.jpg",
    "status": "Active",
    "is_verified": true,
    "created_at": "2024-01-01 10:00:00"
  }
}
```

---

## Error Responses

### 404 Not Found
```json
{
  "success": false,
  "message": "User not found.",
  "data": null
}
```

### 400 Bad Request
```json
{
  "success": false,
  "message": "Invalid UUID format.",
  "data": null
}
```

---

## Admin User APIs

**Base URL:** `/api/v1/auth`
**Authentication:** Required (JWT Token)
**Permissions:** Cần có role `super-admin` hoặc permissions tương ứng

### Permissions Required:
- `user.read` - Xem danh sách và chi tiết user
- `user.create` - Tạo user mới
- `user.update` - Cập nhật user
- `user.destroy` - Xóa user (soft delete)
- `user.restore` - Khôi phục user đã xóa
- `user.force-delete` - Xóa vĩnh viễn user

### 1. Get Users List
Lấy danh sách users với filtering và pagination.

**Endpoint:** `GET /users`

**Query Parameters:**
- `limit` (integer, optional): Số lượng items per page (default: 10)
- `page` (integer, optional): Trang hiện tại (default: 1)
- `status` (string, optional): Filter theo status (`active`, `inactive`, `pending`, `banned`, `suspended`)
- `gender` (string, optional): Filter theo gender (`male`, `female`, `other`)
- `is_verified` (boolean, optional): Filter theo verification status
- `country_id` (integer, optional): Filter theo country ID
- `geo_division_id` (integer, optional): Filter theo geo division ID
- `search` (string, optional): Tìm kiếm theo username, email, first_name, last_name
- `created_from` (date, optional): Filter từ ngày tạo (Y-m-d)
- `created_to` (date, optional): Filter đến ngày tạo (Y-m-d)
- `with_trashed` (boolean, optional): Bao gồm users đã xóa

**Response:**
```json
{
  "success": true,
  "message": "Users retrieved successfully.",
  "data": [
    {
      "id": 1,
      "uuid": "550e8400-e29b-41d4-a716-************",
      "username": "johndoe",
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>",
      "avatar": "storage/avatars/user.jpg",
      "birthday": "1990-01-15",
      "gender": "male",
      "phone": "+84901234567",
      "address": "123 Main St",
      "status": "active",
      "is_verified": true,
      "newsletter_subscribed": false,
      "last_login_at": "2024-01-15 14:30:00",
      "last_login_ip": "***********",
      "geo_division": {
        "id": 1,
        "name": "Ho Chi Minh City",
        "type": "city"
      },
      "country": {
        "id": 1,
        "name": "Vietnam",
        "code": "VN"
      },
      "roles": [
        {
          "id": 2,
          "name": "user",
          "display_name": "User"
        }
      ],
      "created_at": "2024-01-01 10:00:00",
      "updated_at": "2024-01-15 14:30:00",
      "deleted_at": null
    }
  ],
  "total": 100,
  "limit": 10
}
```

### 2. Get User Details
Lấy chi tiết một user theo ID.

**Endpoint:** `GET /users/{id}`

**Parameters:**
- `id` (integer, required): ID của user

**Response:**
```json
{
  "success": true,
  "message": "User retrieved successfully.",
  "data": {
    "id": 1,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "username": "johndoe",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "avatar": "storage/avatars/user.jpg",
    "birthday": "1990-01-15",
    "gender": "male",
    "phone": "+84901234567",
    "address": "123 Main St",
    "status": "active",
    "is_verified": true,
    "newsletter_subscribed": false,
    "last_login_at": "2024-01-15 14:30:00",
    "last_login_ip": "***********",
    "preferences": {
      "theme": "dark",
      "language": "en"
    },
    "geo_division": {
      "id": 1,
      "name": "Ho Chi Minh City",
      "type": "city"
    },
    "country": {
      "id": 1,
      "name": "Vietnam",
      "code": "VN"
    },
    "roles": [
      {
        "id": 2,
        "name": "user",
        "display_name": "User"
      }
    ],
    "permissions": [
      {
        "id": 1,
        "name": "user.read",
        "display_name": "Read Users"
      }
    ],
    "created_at": "2024-01-01 10:00:00",
    "updated_at": "2024-01-15 14:30:00",
    "deleted_at": null
  }
}
```

### 3. Create User
Tạo user mới trong hệ thống.

**Endpoint:** `POST /users`

**Request Body:**
```json
{
  "username": "johndoe",
  "password": "password123",
  "password_confirmation": "password123",
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "avatar": "storage/avatars/user.jpg",
  "birthday": "1990-01-15",
  "gender": "male",
  "phone": "+84901234567",
  "address": "123 Main St",
  "geo_division_id": 1,
  "country_id": 1,
  "status": "active",
  "is_verified": true,
  "newsletter_subscribed": false,
  "roles": [2, 3]
}
```

**Response:**
```json
{
  "success": true,
  "message": "User created successfully.",
  "data": {
    "id": 1,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "username": "johndoe",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "avatar": "storage/avatars/user.jpg",
    "birthday": "1990-01-15",
    "gender": "male",
    "phone": "+84901234567",
    "address": "123 Main St",
    "status": "active",
    "is_verified": true,
    "newsletter_subscribed": false,
    "geo_division": {
      "id": 1,
      "name": "Ho Chi Minh City",
      "type": "city"
    },
    "country": {
      "id": 1,
      "name": "Vietnam",
      "code": "VN"
    },
    "roles": [
      {
        "id": 2,
        "name": "user",
        "display_name": "User"
      }
    ],
    "permissions": [],
    "created_at": "2024-01-01 10:00:00",
    "updated_at": "2024-01-01 10:00:00",
    "deleted_at": null
  }
}
```

### 4. Update User
Cập nhật thông tin user.

**Endpoint:** `PUT /users/{id}`

**Parameters:**
- `id` (integer, required): ID của user

**Request Body:**
```json
{
  "username": "johndoe_updated",
  "first_name": "John Updated",
  "last_name": "Doe Updated",
  "email": "<EMAIL>",
  "avatar": "storage/avatars/user_updated.jpg",
  "birthday": "1990-01-15",
  "gender": "male",
  "phone": "+84901234568",
  "address": "456 Updated St",
  "geo_division_id": 2,
  "country_id": 1,
  "status": "active",
  "is_verified": true,
  "newsletter_subscribed": true,
  "roles": [2, 3, 4]
}
```

**Response:**
```json
{
  "success": true,
  "message": "User updated successfully.",
  "data": {
    "id": 1,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "username": "johndoe_updated",
    "first_name": "John Updated",
    "last_name": "Doe Updated",
    "email": "<EMAIL>",
    "avatar": "storage/avatars/user_updated.jpg",
    "birthday": "1990-01-15",
    "gender": "male",
    "phone": "+84901234568",
    "address": "456 Updated St",
    "status": "active",
    "is_verified": true,
    "newsletter_subscribed": true,
    "geo_division": {
      "id": 2,
      "name": "Hanoi",
      "type": "city"
    },
    "country": {
      "id": 1,
      "name": "Vietnam",
      "code": "VN"
    },
    "roles": [
      {
        "id": 2,
        "name": "user",
        "display_name": "User"
      },
      {
        "id": 3,
        "name": "editor",
        "display_name": "Editor"
      }
    ],
    "permissions": [],
    "created_at": "2024-01-01 10:00:00",
    "updated_at": "2024-01-15 14:30:00",
    "deleted_at": null
  }
}
```

### 5. Delete User (Soft Delete)
Xóa user (soft delete).

**Endpoint:** `DELETE /users/{id}`

**Parameters:**
- `id` (integer, required): ID của user

**Response:**
```json
{
  "success": true,
  "message": "User deleted successfully.",
  "data": null
}
```

### 6. Restore User
Khôi phục user đã bị xóa.

**Endpoint:** `PUT /users/{id}/restore`

**Parameters:**
- `id` (integer, required): ID của user

**Response:**
```json
{
  "success": true,
  "message": "User restored successfully.",
  "data": {
    "id": 1,
    "uuid": "550e8400-e29b-41d4-a716-************",
    "username": "johndoe",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "status": "active",
    "deleted_at": null,
    "created_at": "2024-01-01 10:00:00",
    "updated_at": "2024-01-15 14:30:00"
  }
}
```

### 7. Force Delete User
Xóa vĩnh viễn user khỏi database.

**Endpoint:** `DELETE /users/{id}/force-delete`

**Parameters:**
- `id` (integer, required): ID của user

**Response:**
```json
{
  "success": true,
  "message": "User permanently deleted.",
  "data": null
}
```

## Bulk Operations

### 8. Bulk Delete Users (Soft Delete)
Xóa nhiều users cùng lúc (soft delete).

**Endpoint:** `DELETE /users/bulk-destroy`

**Request Body:**
```json
{
  "ids": [1, 2, 3, 4, 5]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Users deleted successfully.",
  "data": {
    "deleted_count": 5,
    "deleted_ids": [1, 2, 3, 4, 5]
  }
}
```

### 9. Bulk Restore Users
Khôi phục nhiều users đã bị xóa.

**Endpoint:** `PUT /users/bulk-restore`

**Request Body:**
```json
{
  "ids": [1, 2, 3, 4, 5]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Users restored successfully.",
  "data": {
    "restored_count": 5,
    "restored_ids": [1, 2, 3, 4, 5]
  }
}
```

### 10. Bulk Force Delete Users
Xóa vĩnh viễn nhiều users khỏi database.

**Endpoint:** `DELETE /users/bulk-delete`

**Request Body:**
```json
{
  "ids": [1, 2, 3, 4, 5]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Users permanently deleted.",
  "data": {
    "deleted_count": 5,
    "deleted_ids": [1, 2, 3, 4, 5]
  }
}
```

---

## Request Validation

### UserRequest
Class validation cho tạo và cập nhật user.

**File:** `Modules\User\app\Http\Requests\UserRequest.php`

#### Validation Rules

| Field | Rules | Description |
|-------|-------|-------------|
| `username` | required, string, min:3, max:50, regex, unique | Username phải từ 3-50 ký tự, bắt đầu bằng chữ cái, chỉ chứa chữ cái, số, dấu gạch ngang và gạch dưới |
| `password` | required (POST), nullable (PUT), string, min:8, confirmed | Mật khẩu tối thiểu 8 ký tự, cần xác nhận |
| `first_name` | required, string, max:100 | Tên không được để trống |
| `last_name` | required, string, max:100 | Họ không được để trống |
| `email` | required, string, email, max:255, unique | Email hợp lệ và duy nhất |
| `avatar` | nullable, string, max:500 | Đường dẫn avatar |
| `birthday` | nullable, date, before:today | Ngày sinh phải trước hôm nay |
| `gender` | required, string, in:male,female,other | Giới tính hợp lệ |
| `phone` | nullable, string, min:7, max:15, regex, unique | Số điện thoại hợp lệ và duy nhất |
| `address` | nullable, string, max:1000 | Địa chỉ |
| `geo_division_id` | nullable, integer, exists:geo_divisions,id | ID phân vùng địa lý hợp lệ |
| `country_id` | nullable, integer, exists:countries,id | ID quốc gia hợp lệ |
| `status` | required, string, in:active,inactive,pending,banned,suspended | Trạng thái hợp lệ |
| `is_verified` | nullable, boolean | Trạng thái xác thực |
| `newsletter_subscribed` | nullable, boolean | Đăng ký newsletter |
| `roles` | nullable, array, exists:roles,id | Mảng ID roles hợp lệ |

#### Username Regex Pattern
```regex
/^[a-zA-Z][a-zA-Z0-9_-]*[a-zA-Z0-9]$/
```
- Bắt đầu bằng chữ cái
- Chỉ chứa chữ cái, số, dấu gạch ngang và gạch dưới
- Kết thúc bằng chữ cái hoặc số

#### Phone Regex Pattern
```regex
/^(\+\d{1,3}|0)[0-9\s\-\(\)]{6,12}$/
```
- Bắt đầu bằng mã quốc gia (+xxx) hoặc số 0
- Chứa 6-12 ký tự số, khoảng trắng, dấu gạch ngang, dấu ngoặc

#### Custom Error Messages

```json
{
  "username.required": "Username is required.",
  "username.unique": "This username is already taken.",
  "username.regex": "Username may only contain letters, numbers, dashes and underscores.",
  "password.required": "Password is required.",
  "password.min": "Password must be at least 8 characters.",
  "password.confirmed": "Password confirmation does not match.",
  "first_name.required": "First name is required.",
  "last_name.required": "Last name is required.",
  "email.required": "Email is required.",
  "email.email": "Please provide a valid email address.",
  "email.unique": "This email is already registered.",
  "phone.unique": "This phone number is already registered.",
  "phone.regex": "Please provide a valid phone number.",
  "birthday.before": "Birthday must be a date before today.",
  "gender.required": "Gender is required.",
  "gender.in": "Please select a valid gender.",
  "status.required": "Status is required.",
  "status.in": "Please select a valid status.",
  "geo_division_id.exists": "Selected geographic division does not exist.",
  "country_id.exists": "Selected country does not exist.",
  "roles.*.exists": "One or more selected roles do not exist."
}
```

### BulkUserRequest
Class validation cho bulk operations.

**File:** `Modules\User\app\Http\Requests\BulkUserRequest.php`

#### Validation Rules

| Field | Rules | Description |
|-------|-------|-------------|
| `ids` | required, array, min:1 | Mảng IDs không được rỗng |
| `ids.*` | required, integer, exists:users,id | Mỗi ID phải là số nguyên và tồn tại trong bảng users |

#### Custom Error Messages

```json
{
  "ids.required": "Please select at least one user.",
  "ids.array": "Invalid data format.",
  "ids.min": "Please select at least one user.",
  "ids.*.required": "User ID is required.",
  "ids.*.integer": "User ID must be a number.",
  "ids.*.exists": "One or more selected users do not exist."
}
```

### Validation Error Response

Khi validation thất bại, API sẽ trả về response với status code 422:

```json
{
  "success": false,
  "message": "The given data was invalid.",
  "errors": {
    "username": [
      "This username is already taken."
    ],
    "email": [
      "Please provide a valid email address."
    ],
    "password": [
      "Password must be at least 8 characters.",
      "Password confirmation does not match."
    ]
  }
}
```

---

## Response Structures

### UserPublicResource
Resource class cho public user information.

**File:** `Modules\User\app\Http\Resources\UserPublicResource.php`

**Structure:**
```json
{
  "uuid": "550e8400-e29b-41d4-a716-************",
  "username": "johndoe",
  "full_name": "John Doe",
  "first_name": "John",
  "last_name": "Doe",
  "avatar_url": "https://domain.com/storage/avatars/user.jpg",
  "gender": {
    "value": "male",
    "label": "Male",
    "icon": "fas fa-mars"
  },
  "status": {
    "value": "active",
    "label": "Active",
    "css_class": "success"
  },
  "is_verified": true,
  "newsletter_subscribed": false,
  "location": {
    "country": {
      "id": 1,
      "name": "Vietnam",
      "code": "VN"
    },
    "geo_division": {
      "id": 1,
      "name": "Ho Chi Minh City",
      "type": "city"
    }
  },
  "created_at": "2024-01-01 10:00:00",
  "updated_at": "2024-01-15 14:30:00"
}
```

### Standard Response Format

#### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully.",
  "data": { ... }
}
```

#### Error Response
```json
{
  "success": false,
  "message": "Error message describing what went wrong.",
  "data": null
}
```

#### Paginated Response
```json
{
  "success": true,
  "message": "Data retrieved successfully.",
  "data": [...],
  "total": 100,
  "limit": 10
}
```

### HTTP Status Codes

| Code | Description | Usage |
|------|-------------|-------|
| 200 | OK | Successful GET, PUT requests |
| 201 | Created | Successful POST requests |
| 400 | Bad Request | Invalid request data |
| 401 | Unauthorized | Missing or invalid authentication |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Resource not found |
| 422 | Unprocessable Entity | Validation errors |
| 500 | Internal Server Error | Server errors |

### Error Response Examples

#### 401 Unauthorized
```json
{
  "success": false,
  "message": "Unauthenticated.",
  "data": null
}
```

#### 403 Forbidden
```json
{
  "success": false,
  "message": "This action is unauthorized.",
  "data": null
}
```

#### 404 Not Found
```json
{
  "success": false,
  "message": "User not found.",
  "data": null
}
```

#### 422 Validation Error
```json
{
  "success": false,
  "message": "The given data was invalid.",
  "errors": {
    "email": [
      "The email field is required."
    ],
    "username": [
      "This username is already taken."
    ]
  }
}
```

#### 500 Server Error
```json
{
  "success": false,
  "message": "An error occurred. Please try again later.",
  "data": null
}
```

### Field Descriptions

#### User Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Internal user ID (hidden in public APIs) |
| `uuid` | string | Public user identifier |
| `username` | string | Unique username |
| `first_name` | string | User's first name |
| `last_name` | string | User's last name |
| `full_name` | string | Computed full name (first_name + last_name) |
| `email` | string | User's email address |
| `avatar` | string | Avatar file path |
| `avatar_url` | string | Full avatar URL with domain |
| `birthday` | date | User's birth date (Y-m-d) |
| `gender` | enum | User's gender (male, female, other) |
| `phone` | string | User's phone number |
| `address` | string | User's address |
| `status` | enum | User's status (active, inactive, pending, banned, suspended) |
| `is_verified` | boolean | Email verification status |
| `newsletter_subscribed` | boolean | Newsletter subscription status |
| `last_login_at` | datetime | Last login timestamp |
| `last_login_ip` | string | Last login IP address |
| `preferences` | object | User preferences JSON |
| `geo_division_id` | integer | Geographic division ID |
| `country_id` | integer | Country ID |
| `created_at` | datetime | Creation timestamp |
| `updated_at` | datetime | Last update timestamp |
| `deleted_at` | datetime | Soft delete timestamp |

#### Relationship Fields

| Field | Type | Description |
|-------|------|-------------|
| `geo_division` | object | Geographic division information |
| `country` | object | Country information |
| `roles` | array | User's assigned roles |
| `permissions` | array | User's direct permissions |

---

## User Enums

### UserStatus Enum
Enum định nghĩa các trạng thái của user.

**File:** `Modules\User\app\Enums\UserStatus.php`

#### Available Values

| Value | Label | CSS Class | Description |
|-------|-------|-----------|-------------|
| `active` | Active | success | User đang hoạt động bình thường |
| `inactive` | Inactive | secondary | User không hoạt động |
| `pending` | Pending | warning | User đang chờ xác thực |
| `banned` | Banned | danger | User bị cấm |
| `suspended` | Suspended | dark | User bị tạm ngưng |

#### Methods

```php
// Get all values
UserStatus::values(); // ['active', 'inactive', 'pending', 'banned', 'suspended']

// Get all names
UserStatus::names(); // ['Active', 'Inactive', 'Pending', 'Banned', 'Suspended']

// Get options for dropdown
UserStatus::options(); // ['active' => 'Active', 'inactive' => 'Inactive', ...]

// Instance methods
$status = UserStatus::Active;
$status->label(); // 'Active'
$status->cssClass(); // 'success'
$status->isActive(); // true
$status->canLogin(); // true

// Static methods
UserStatus::loginAllowed(); // ['active']
UserStatus::inactive(); // ['inactive', 'banned', 'suspended']
```

#### Usage in Responses

```json
{
  "status": {
    "value": "active",
    "label": "Active",
    "css_class": "success"
  }
}
```

### UserGender Enum
Enum định nghĩa giới tính của user.

**File:** `Modules\User\app\Enums\UserGender.php`

#### Available Values

| Value | Label | Icon | CSS Class | Description |
|-------|-------|------|-----------|-------------|
| `male` | Male | fas fa-mars | text-primary | Nam |
| `female` | Female | fas fa-venus | text-danger | Nữ |
| `other` | Other | fas fa-genderless | text-secondary | Khác |

#### Methods

```php
// Get all values
UserGender::values(); // ['male', 'female', 'other']

// Get all names
UserGender::names(); // ['Male', 'Female', 'Other']

// Get options for dropdown
UserGender::options(); // ['male' => 'Male', 'female' => 'Female', 'other' => 'Other']

// Instance methods
$gender = UserGender::Male;
$gender->label(); // 'Male'
$gender->icon(); // 'fas fa-mars'
$gender->cssClass(); // 'text-primary'
```

#### Usage in Responses

```json
{
  "gender": {
    "value": "male",
    "label": "Male",
    "icon": "fas fa-mars"
  }
}
```

### Enum Usage Examples

#### In Validation Rules
```php
// UserRequest.php
'status' => [
    'required',
    'string',
    Rule::in(UserStatus::values()),
],
'gender' => [
    'required',
    'string',
    Rule::in(UserGender::values()),
],
```

#### In Model Casting
```php
// User.php
protected $casts = [
    'status' => UserStatus::class,
    'gender' => UserGender::class,
];
```

#### In Queries
```php
// Get active users
$activeUsers = User::where('status', UserStatus::Active)->get();

// Get male users
$maleUsers = User::where('gender', UserGender::Male)->get();

// Check user status
if ($user->status->canLogin()) {
    // User can login
}
```

#### In Frontend
```php
// Get status options for select dropdown
$statusOptions = UserStatus::options();

// Get gender options for radio buttons
$genderOptions = UserGender::options();
```

---

## API Usage Examples

### Authentication Headers

#### For Admin APIs
```bash
curl -X GET "https://domain.com/api/v1/auth/users" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

### Complete CRUD Example

#### 1. Create User
```bash
curl -X POST "https://domain.com/api/v1/auth/users" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "johndoe",
    "password": "password123",
    "password_confirmation": "password123",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "gender": "male",
    "status": "active",
    "roles": [2]
  }'
```

#### 2. Get Users List with Filters
```bash
curl -X GET "https://domain.com/api/v1/auth/users?status=active&gender=male&limit=20&search=john" \
  -H "Authorization: Bearer your-jwt-token"
```

#### 3. Update User
```bash
curl -X PUT "https://domain.com/api/v1/auth/users/1" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "first_name": "John Updated",
    "status": "active",
    "roles": [2, 3]
  }'
```

#### 4. Delete User (Soft Delete)
```bash
curl -X DELETE "https://domain.com/api/v1/auth/users/1" \
  -H "Authorization: Bearer your-jwt-token"
```

#### 5. Restore User
```bash
curl -X PUT "https://domain.com/api/v1/auth/users/1/restore" \
  -H "Authorization: Bearer your-jwt-token"
```

### Public API Examples

#### Get User by Username
```bash
curl -X GET "https://domain.com/api/v1/members/johndoe"
```

#### Get User by UUID
```bash
curl -X GET "https://domain.com/api/v1/users/public/550e8400-e29b-41d4-a716-************"
```

#### Check User Exists
```bash
curl -X GET "https://domain.com/api/v1/users/public/550e8400-e29b-41d4-a716-************/exists"
```

### Bulk Operations Examples

#### Bulk Delete
```bash
curl -X DELETE "https://domain.com/api/v1/auth/users/bulk-destroy" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [1, 2, 3, 4, 5]
  }'
```

#### Bulk Restore
```bash
curl -X PUT "https://domain.com/api/v1/auth/users/bulk-restore" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [1, 2, 3, 4, 5]
  }'
```

---

## Best Practices

### Security Considerations

1. **Always use HTTPS** in production
2. **Validate JWT tokens** for admin endpoints
3. **Check permissions** before allowing operations
4. **Hide sensitive data** in public APIs
5. **Use UUID** instead of ID in public endpoints
6. **Sanitize input data** to prevent XSS/SQL injection
7. **Rate limit** API requests to prevent abuse

### Performance Tips

1. **Use pagination** for large datasets
2. **Implement caching** for frequently accessed data
3. **Use eager loading** for relationships
4. **Index database columns** used in filters
5. **Optimize queries** to avoid N+1 problems

### Error Handling

1. **Always return consistent response format**
2. **Use appropriate HTTP status codes**
3. **Provide meaningful error messages**
4. **Log errors** for debugging
5. **Don't expose sensitive information** in error messages

### API Versioning

1. **Use URL versioning** (`/api/v1/`)
2. **Maintain backward compatibility**
3. **Document breaking changes**
4. **Provide migration guides**

---

## Testing

### Unit Tests
```bash
# Run User module tests
php artisan test Modules/User/tests/

# Run specific test
php artisan test Modules/User/tests/Feature/UserControllerTest.php
```

### API Testing with Postman

1. Import the API collection
2. Set environment variables:
   - `base_url`: Your API base URL
   - `jwt_token`: Your authentication token
3. Run the collection tests

### Manual Testing Checklist

- [ ] Public APIs work without authentication
- [ ] Admin APIs require valid JWT token
- [ ] Validation rules work correctly
- [ ] Permissions are enforced
- [ ] Pagination works properly
- [ ] Filtering works as expected
- [ ] Error responses are consistent
- [ ] Bulk operations work correctly

---

## Troubleshooting

### Common Issues

#### 401 Unauthorized
- Check if JWT token is valid
- Verify token is included in Authorization header
- Ensure token hasn't expired

#### 403 Forbidden
- Check user permissions
- Verify user has required role
- Ensure user account is active

#### 422 Validation Error
- Check request body format
- Verify all required fields are provided
- Ensure data types match validation rules

#### 404 Not Found
- Verify endpoint URL is correct
- Check if resource exists
- Ensure user has access to resource

#### 500 Server Error
- Check server logs
- Verify database connection
- Ensure all dependencies are installed

### Debug Mode

Enable debug mode in `.env` for detailed error messages:
```env
APP_DEBUG=true
```

**Note:** Never enable debug mode in production!

---

## Changelog

### Version 1.0.0
- Initial API implementation
- Basic CRUD operations
- Public user endpoints
- Bulk operations
- Comprehensive validation
- Role-based permissions

---

## Support

For technical support or questions about the User API:

1. Check this documentation first
2. Review the source code in `Modules/User/`
3. Check existing issues in the project repository
4. Contact the development team

---

**Last Updated:** 2024-01-15
**API Version:** v1
**Documentation Version:** 1.0.0

<?php

use Illuminate\Support\Facades\Route;
use Modules\Auth\Http\Controllers\AuthController;

// OAuth routes - these need to be web routes for proper redirect handling
Route::prefix('auth/social')->name('auth.oauth.')->group(function () {
    // OAuth redirect routes
    Route::get('{provider}', [AuthController::class, 'redirectToProvider'])
        ->middleware('auth.rate.limit:oauth')
        ->name('redirect');

    // OAuth callback routes
    Route::get('{provider}/callback', [AuthController::class, 'handleProviderCallback'])
        ->middleware('auth.rate.limit:oauth')
        ->name('callback');
});

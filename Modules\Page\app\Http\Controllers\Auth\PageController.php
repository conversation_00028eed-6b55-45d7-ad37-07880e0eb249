<?php

namespace Modules\Page\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Modules\Core\Traits\ResponseTrait;
use Modules\Page\Models\Page;
use Modules\Page\Http\Requests\PageRequest;
use Modules\Page\Http\Requests\BulkPageRequest;
use Modules\Page\Http\Requests\BulkPageDestroyRequest;
use Modules\Page\Http\Filters\PageFilter;
use Throwable;

class PageController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {
        $this->middleware('role_or_permission:super-admin|page.view')->only(['index', 'show']);
        $this->middleware('role_or_permission:super-admin|page.create')->only(['store']);
        $this->middleware('role_or_permission:super-admin|page.edit')->only(['update']);
        $this->middleware('role_or_permission:super-admin|page.delete')->only(['delete', 'restore', 'bulkDelete', 'bulkRestore']);
        $this->middleware('role_or_permission:super-admin|page.destroy')->only(['destroy', 'bulkDestroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $pages = Page::query()
            ->filter(new PageFilter($request))
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($pages, __('Languages retrieved successfully.'));
    }

    /**
     * Store a newly created resource in storage.
     */
    /**
     * Store a newly created resource in storage.
     * @throws Throwable
     */
    public function store(PageRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();
            $page = Page::create($request->all());
            if ($request->has('translations')) {
                foreach ($request->input('translations', []) as $locale => $translate) {
                    $page->translateOrNew($locale)->fill($translate)->save();
                }
            }
            DB::commit();
            return $this->successResponse($page->fresh(), __('Page created successfully.'), 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse($request->all(), __('Page creation failed.'), 422);
        }
    }

    /**
     * Show the specified resource.
     */
    public function show(Request $request, Page $page): JsonResponse
    {
        $page->load(['translations', 'author']);
        return $this->successResponse($page, __('Page retrieved successfully.'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Page $page): JsonResponse
    {
        $page->load(['translations', 'author']);
        return $this->successResponse($page, __('Page retrieved successfully.'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @throws Throwable
     *
     */
    public function update(PageRequest $request, Page $page): JsonResponse
    {
        try {
            DB::beginTransaction();
            $page->update($request->all());

            if ($request->has('translations')) {
                foreach ($request->input('translations') as $locale => $translation) {
                    $page->translateOrNew($locale)->fill($translation)->save();
                }
            }

            DB::commit();

            $page->load('translations');

            return $this->successResponse($page->fresh(), __('Page updated successfully!'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse(__('Page update failed!'), 422);
        }
    }

    /**
     * Soft delete the specified resource from storage.
     */
    public function delete(int $id): JsonResponse
    {
        $page = Page::findOrFail($id);
        $page->delete();
        return $this->successResponse(null, __('Page move to trash successfully!'));
    }

    public function restore(int $id): JsonResponse
    {
        $page = Page::onlyTrashed()->findOrFail($id);
        $page->restore();

        return $this->successResponse($page, __('Page restored successfully.'));
    }

    public function destroy(int $id): JsonResponse
    {
        $page = Page::onlyTrashed()->findOrFail($id);
        $page->forceDelete();

        return $this->successResponse($id, __('Page permanently deleted.'));
    }

    public function bulkDelete(BulkPageRequest $request): JsonResponse
    {
        $pages = Page::whereIn('id', $request->validated('ids'))->delete();
        return $this->successResponse(
            $request->validated('ids'),
            __('messages.pages.bulk_moved_to_trash', ['rows_count' => $pages])
        );
    }

    public function bulkRestore(BulkPageRequest $request): JsonResponse
    {
        $restoredCount = Page::onlyTrashed()
            ->whereIn('id', $request->validated('ids'))
            ->restore();

        return $this->successResponse(
            ['restored_count' => $restoredCount],
            __(':count page restored successfully.', ['count' => $restoredCount])
        );
    }

    public function bulkDestroy(BulkPageDestroyRequest $request): JsonResponse
    {
        $deletedCount = Page::onlyTrashed()
            ->whereIn('id', $request->validated('ids'))
            ->forceDelete();

        return $this->successResponse(
            ['deleted_count' => $deletedCount],
            __(':count page permanently deleted.', ['count' => $deletedCount])
        );
    }
}

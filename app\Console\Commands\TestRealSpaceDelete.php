<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\Organization\Models\Organization;
use Modules\User\Models\User;
use Modules\Organization\Http\Controllers\Auth\OrganizationController;
use Modules\Organization\Http\Requests\Organization\DestroyOrganizationRequest;
use Illuminate\Http\Request;

class TestRealSpaceDelete extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:real-space-delete {user_id} {uuid}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test real space deletion through controller';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id');
        $uuid = $this->argument('uuid');
        
        $user = User::find($userId);
        
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return;
        }
        
        // Find organization by UUID
        $organization = Organization::where('uuid', $uuid)->first();
        
        if (!$organization) {
            $this->error("Organization with UUID {$uuid} not found");
            return;
        }

        $this->info("Testing real space deletion through controller:");
        $this->info("User: {$user->email} (ID: {$user->id})");
        $this->info("Organization: {$organization->name} (ID: {$organization->id})");
        $this->info("Organization UUID: {$organization->uuid}");
        $this->info("Organization Owner ID: {$organization->owner_id}");
        
        try {
            // Create a real HTTP request
            $request = Request::create("/api/v1/auth/spaces/{$uuid}", 'DELETE');
            $request->setUserResolver(function() use ($user) {
                return $user;
            });
            
            // Create DestroyOrganizationRequest
            $destroyRequest = DestroyOrganizationRequest::createFrom($request);
            $destroyRequest->setUserResolver(function() use ($user) {
                return $user;
            });
            
            // Mock route
            $destroyRequest->setRouteResolver(function() use ($organization, $uuid) {
                $route = new \Illuminate\Routing\Route(['DELETE'], '/api/v1/auth/spaces/{space}', []);
                $route->bind($request = Request::create("/api/v1/auth/spaces/{$uuid}", 'DELETE'));
                $route->setParameter('space', $organization);
                return $route;
            });
            
            // Test authorization
            $this->info("Testing DestroyOrganizationRequest authorization...");
            $canAuthorize = $destroyRequest->authorize();
            $this->info("Authorization result: " . ($canAuthorize ? 'ALLOWED' : 'DENIED'));
            
            if (!$canAuthorize) {
                $this->error("✗ Authorization failed");
                
                // Debug information
                $this->info("Debug information:");
                $this->info("- User ID: {$user->id}");
                $this->info("- Organization Owner ID: {$organization->owner_id}");
                $this->info("- Is owner: " . ($user->id === $organization->owner_id ? 'YES' : 'NO'));
                $this->info("- Policy allows: " . ($user->can('delete', $organization) ? 'YES' : 'NO'));
                
                return;
            }
            
            // Test controller method
            $this->info("Testing controller destroy method...");
            $controller = new OrganizationController();
            
            try {
                $response = $controller->destroy($destroyRequest, $organization);
                $this->info("✓ Controller method executed successfully");
                $this->info("Response: " . $response->getContent());
                
                // Check if organization was actually deleted
                $deletedOrg = Organization::withTrashed()->find($organization->id);
                if ($deletedOrg && $deletedOrg->trashed()) {
                    $this->info("✓ Organization was soft deleted successfully");
                } else {
                    $this->error("✗ Organization was not deleted");
                }
                
            } catch (\Exception $e) {
                $this->error("✗ Controller method failed:");
                $this->error($e->getMessage());
                $this->error("File: {$e->getFile()}:{$e->getLine()}");
            }
            
        } catch (\Exception $e) {
            $this->error("✗ Failed to test real space deletion:");
            $this->error($e->getMessage());
            $this->error("File: {$e->getFile()}:{$e->getLine()}");
            
            if ($this->option('verbose')) {
                $this->error("Stack trace:");
                $this->error($e->getTraceAsString());
            }
        }
    }
}

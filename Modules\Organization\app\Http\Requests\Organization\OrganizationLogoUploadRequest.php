<?php

namespace Modules\Organization\Http\Requests\Organization;

use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Organization\Models\Organization;

class OrganizationLogoUploadRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'logo' => [
                'required',
                'file',
                'image',
                'mimes:jpeg,jpg,png,gif,svg,webp',
                'max:10240', // 10MB
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'logo.required' => __('Logo file is required.'),
            'logo.file' => __('Logo must be a valid file.'),
            'logo.image' => __('Logo must be an image file.'),
            'logo.mimes' => __('Logo must be a file of type: jpeg, jpg, png, gif, svg, webp.'),
            'logo.max' => __('Logo file size cannot exceed 10MB.'),
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Allow all authenticated users to upload logo to temp directory
        return $this->user() !== null;
    }
}

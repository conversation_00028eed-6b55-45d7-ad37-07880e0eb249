<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('auth_login_attempts', function (Blueprint $table) {
            $table->id();
            $table->string('identifier')->index(); // email, username, or IP
            $table->string('ip_address', 45)->index(); // Support IPv6
            $table->text('user_agent')->nullable();
            $table->string('action', 50)->index(); // login, register, password_reset, etc.
            $table->boolean('successful')->default(false)->index();
            $table->string('failure_reason')->nullable(); // invalid_credentials, rate_limited, etc.
            $table->timestamp('attempted_at')->index();
            $table->json('additional_data')->nullable(); // Extra context data
            $table->timestamps();

            // Composite indexes for performance
            $table->index(['identifier', 'action', 'attempted_at']);
            $table->index(['ip_address', 'action', 'attempted_at']);
            $table->index(['successful', 'attempted_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('auth_login_attempts');
    }
};

<?php

namespace Modules\ChatBot\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;

class AttachToBotRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'bot_id' => 'required|integer|exists:bots,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'bot_id.required' => 'Bot ID is required.',
            'bot_id.integer' => 'Bot ID must be an integer.',
            'bot_id.exists' => 'The selected bot does not exist.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'bot_id' => 'bot ID',
        ];
    }
}

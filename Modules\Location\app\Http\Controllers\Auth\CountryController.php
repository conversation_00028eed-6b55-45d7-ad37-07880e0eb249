<?php

namespace Modules\Location\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Modules\Core\Traits\ResponseTrait;
use Modules\Location\Http\Filters\CountryFilter;
use Modules\Location\Models\Country;
use Modules\Location\Http\Requests\CountryRequest;
use Modules\Location\Http\Requests\BulkCountryRequest;
use Modules\Location\Http\Requests\BulkCountryDestroyRequest;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CountryController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {
        $this->middleware('role_or_permission:super-admin|location.country.view')->only(['index', 'show', 'trashed', 'dropdown']);
        $this->middleware('role_or_permission:super-admin|location.country.create')->only(['store']);
        $this->middleware('role_or_permission:super-admin|location.country.edit')->only(['update']);
        $this->middleware('role_or_permission:super-admin|location.country.delete')->only(['delete', 'restore', 'bulkDelete', 'bulkRestore']);
        $this->middleware('role_or_permission:super-admin|location.country.destroy')->only(['destroy', 'bulkDestroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $countries = Country::query()
            ->filter(new CountryFilter($request))
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($countries, __('Countries retrieved successfully.'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CountryRequest $request): JsonResponse
    {
        $country = Country::create($request->all());

        return $this->successResponse($country, __('Country created successfully.'), 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Country $country): JsonResponse
    {
        return $this->successResponse($country, __('Country retrieved successfully.'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CountryRequest $request, Country $country): JsonResponse
    {
        $country->update($request->all());

        return $this->successResponse($country->fresh(), __('Country updated successfully.'));
    }

    /**
     * Soft delete the specified resource from storage.
     */
    public function delete(int $id): JsonResponse
    {
        $country = Country::findOrFail($id);
        $country->delete();

        return $this->successResponse($country->id, __('Country deleted successfully.'));
    }

    /**
     * Restore the specified resource from storage.
     */
    public function restore(int $id): JsonResponse
    {
        $country = Country::withTrashed()->findOrFail($id);
        $country->restore();

        return $this->successResponse($country->fresh(), __('Country restored successfully.'));
    }

    /**
     * Permanently delete the specified resource from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        $country = Country::onlyTrashed()->findOrFail($id);
        $country->forceDelete();

        return $this->successResponse($id, __('Country permanently deleted.'));
    }

    /**
     * Display a listing of trashed resources.
     */
    public function trashed(Request $request): JsonResponse
    {
        $countries = Country::onlyTrashed()
            ->filter(new CountryFilter($request))
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($countries, __('Trashed countries retrieved successfully.'));
    }

    /**
     * Bulk soft delete countries.
     */
    public function bulkDelete(BulkCountryRequest $request): JsonResponse
    {
        $ids = $request->validated('ids');
        $deletedCount = Country::whereIn('id', $ids)->delete();

        return $this->successResponse(
            ['deleted_count' => $deletedCount],
            __(':count countries deleted successfully.', ['count' => $deletedCount])
        );
    }

    /**
     * Bulk restore countries.
     */
    public function bulkRestore(BulkCountryRequest $request): JsonResponse
    {
        $ids = $request->validated('ids');
        $restoredCount = Country::onlyTrashed()->whereIn('id', $ids)->restore();

        return $this->successResponse(
            ['restored_count' => $restoredCount],
            __(':count countries restored successfully.', ['count' => $restoredCount])
        );
    }

    /**
     * Bulk permanently delete countries.
     */
    public function bulkDestroy(BulkCountryDestroyRequest $request): JsonResponse
    {
        $ids = $request->validated('ids');
        $deletedCount = Country::onlyTrashed()->whereIn('id', $ids)->forceDelete();

        return $this->successResponse(
            ['deleted_count' => $deletedCount],
            __(':count countries permanently deleted.', ['count' => $deletedCount])
        );
    }

    /**
     * Get countries for dropdown.
     */
    public function dropdown(): JsonResponse
    {
        $countries = Country::query()->active()->get();
        return $this->successResponse(
            $countries->makeHidden(['updated_at', 'created_at']), 
            __('Countries retrieved successfully.')
        );
    }
}

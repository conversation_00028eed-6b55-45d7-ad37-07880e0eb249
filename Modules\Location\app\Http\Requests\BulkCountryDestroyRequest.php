<?php

namespace Modules\Location\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

class BulkCountryDestroyRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ids' => [
                'required',
                'array',
                'min:1',
            ],
            'ids.*' => [
                'required',
                'integer',
                Rule::exists('countries', 'id')->whereNotNull('deleted_at'), // Only soft deleted countries
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'ids' => __('Country IDs'),
            'ids.*' => __('Country ID'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ids.required' => __('Please select at least one country.'),
            'ids.array' => __('Invalid data format.'),
            'ids.min' => __('Please select at least one country.'),
            'ids.*.required' => __('Country ID is required.'),
            'ids.*.integer' => __('Country ID must be an integer.'),
            'ids.*.exists' => __('One or more selected countries must be in trash to be permanently deleted.'),
        ];
    }
}

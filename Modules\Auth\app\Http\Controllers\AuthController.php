<?php

namespace Modules\Auth\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;
use Modules\Auth\Http\Requests\ForgotPasswordRequest;
use Modules\Auth\Http\Requests\ResetPasswordRequest;
use Modules\Core\Traits\ResponseTrait;
use Modules\Auth\Facades\AuthFacade;
use Modules\Auth\Http\Requests\RegisterRequest;
use Modules\Auth\Http\Requests\LoginRequest;
use Modules\Auth\Http\Requests\VerifyEmailRequest;
use Throwable;

class AuthController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {
    }

    /**
     * Register a new user and send verification OTP.
     */
    public function register(RegisterRequest $request): JsonResponse
    {
        if (!setting_bool('auth.registration.enabled', true)) {
            return $this->errorResponse(null, __('Registration is currently disabled.'), 403);
        }

        try {
            $requiredVerification = setting_bool('auth.account.verification.required', true);

            $user = AuthFacade::register($request->all());

            $requiredVerification ? notify('user.registered', $user) :
                notify('welcome', $user);

            return $this->successResponse(
                ['email' => $user['email']],
                $requiredVerification ?
                    __('Registration successful. Please check your email for verification instructions.') :
                    __('Registration successful. Welcome to our platform!')
            );

        } catch (Throwable $e) {
            return $this->errorResponse(
                null,
                __('Registration failed: :error', ['error' => $e->getMessage()]),
                500
            );
        }
    }

    /**
     * Verify email with OTP code.
     */
    public function verifyEmail(VerifyEmailRequest $request): JsonResponse
    {
        $user = AuthFacade::verifyEmail(
            $request->input('email'),
            $request->input('code')
        );

        if (!$user || is_string($user)) {
            return $this->errorResponse(null, $user);
        }

        notify('user.verified', $user);

        return $this->successResponse(
            Arr::only($user, ['username', 'full_name', 'first_name', 'last_name', 'email']),
            __('Email verified successfully.')
        );
    }

    /**
     * Resend email verification OTP.
     */
    public function resendVerification(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email|exists:users,email'
        ]);

        $email = $request->input('email');

        $verificationCode = AuthFacade::resendVerificationCode($email);

        if (!$verificationCode) {
            return $this->errorResponse($email, $verificationCode);
        }

        notify('verification_code_sent', $verificationCode);

        return $this->successResponse(
            $email,
            __('Verification code sent successfully.')
        );
    }

    /**
     * Login user.
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $result = AuthFacade::login($request->validated());

        if (is_array($result)) {
            notify('user.logged-in', [
                'user' => $result['data']['user'],
                'ip' => $request->ip()
            ]);
            unset($result['data']['user']['id']);
            return $this->successResponse($result['data'], __('Login successful.'));
        }
        return $this->errorResponse('', $result);
    }


    /**
     * Send forgot password OTP.
     */
    public function forgotPassword(ForgotPasswordRequest $request): JsonResponse
    {
        $result = AuthFacade::sendPasswordResetCode($request->input('email'));

        if (!$result['success']) {
            return $this->errorResponse(null, $result['message'], 400);
        }
        // Fire password reset notification event
        // event('password_reset', ['data' => $result['user']]);
        notify('password_reset', [
            'user' => $result['user'],
            'reset_code' => $result['reset_code'],
            'expires_in' => 30,
            'expires_at' => $result['expires_at']
        ]);

        return $this->successResponse(
            null,
            __('Password reset code sent to your email.')
        );
    }

    /**
     * Reset password with OTP.
     */
    public function resetPassword(ResetPasswordRequest $request): JsonResponse
    {
        $result = AuthFacade::resetPassword(
            $request->input('email'),
            $request->input('code'),
            $request->input('password')
        );

        if (is_string($result)) {
            return $this->errorResponse(null, $result, 400);
        }

        // Get user data for notification
        $user = \Modules\User\Models\User::where('email', $request->input('email'))->first();
        notify('password_changed', [
            'user' => $user->toArray(),
            'change_time' => now()->format('Y-m-d H:i:s')
        ]);

        return $this->successResponse(
            null,
            __('Password reset successfully.')
        );
    }


    /**
     * Logout user.
     */
    public function logout(): JsonResponse
    {
        AuthFacade::logout();

        return $this->successResponse(
            null,
            __('Logout successful.')
        );
    }

    /**
     * Refresh JWT token.
     */
    public function refresh(): JsonResponse
    {
        $result = AuthFacade::refresh();

        return $this->successResponse(
            $result,
            __('Token refreshed successfully.')
        );
    }

    /**
     * Redirect to OAuth provider.
     */
    public function redirectToProvider(string $provider)
    {
        return AuthFacade::redirectToProvider($provider);
    }

    /**
     * Handle OAuth provider callback.
     */
    public function handleProviderCallback(string $provider): JsonResponse
    {
        $result = AuthFacade::handleProviderCallback($provider);

        if ($result['success']) {
            // notify('auth.user.logged-in', $result['data']['user']);
            return $this->successResponse(
                $result['data'],
                __('OAuth login successful.')
            );
        }

        return $this->errorResponse(
            null,
            $result['message'],
            400
        );
    }

    /**
     * Get enabled OAuth providers.
     */
    public function getEnabledProviders(): JsonResponse
    {
        $providers = AuthFacade::getEnabledProviders();

        return $this->successResponse(
            $providers,
            __('OAuth providers retrieved successfully.')
        );
    }

}

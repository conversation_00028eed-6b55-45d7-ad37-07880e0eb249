<?php

namespace Modules\ChatBot\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;

class KnowledgeBaseBulkUploadRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'files' => 'required|array|max:10',
            'files.*' => 'required|file|mimes:txt,md,doc,docx,xls,xlsx,ppt,pptx,pdf,png,jpg,jpeg|max:102400', // 100MB each
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'files.required' => 'At least one file is required for bulk upload.',
            'files.array' => 'Files must be provided as an array.',
            'files.max' => 'Cannot upload more than 10 files at once.',
            'files.*.required' => 'Each file in the array is required.',
            'files.*.file' => 'Each uploaded item must be a valid file.',
            'files.*.mimes' => 'Each file must be one of the following types: txt, md, doc, docx, xls, xlsx, ppt, pptx, pdf, png, jpg, jpeg.',
            'files.*.max' => 'Each file size cannot exceed 100MB.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'files' => 'files',
            'files.*' => 'file',
        ];
    }
}

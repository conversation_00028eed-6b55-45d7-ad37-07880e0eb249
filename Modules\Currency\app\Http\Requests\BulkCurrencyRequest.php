<?php

namespace Modules\Currency\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;

class BulkCurrencyRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ids' => [
                'required',
                'array',
                'min:1',
            ],
            'ids.*' => [
                'required',
                'integer',
                'exists:currencies,id',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'ids' => __('Currency IDs'),
            'ids.*' => __('Currency ID'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ids.required' => __('Please select at least one currency.'),
            'ids.array' => __('Invalid data format.'),
            'ids.min' => __('Please select at least one currency.'),
            'ids.*.required' => __('Currency ID is required.'),
            'ids.*.integer' => __('Currency ID must be an integer.'),
            'ids.*.exists' => __('One or more selected currencies do not exist.'),
        ];
    }

}

<?php

namespace Modules\ActiveCode\Services;

use Modules\ActiveCode\Models\ActiveCode;
use Modules\Setting\Facades\SettingFacade;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use Throwable;

/**
 * ActiveCodeService
 *
 * Manages active codes including generation, verification, and cleanup.
 * Uses dynamic settings from database with <PERSON><PERSON>'s cache tag mechanism for performance optimization.
 */
class ActiveCodeService
{
    /**
     * Cache tag for all active code settings.
     */
    private const CACHE_TAG = 'active_code_settings';

    /**
     * Cache Time-To-Live in seconds (1 hour).
     */
    private const CACHE_TTL = 3600;

    /**
     * Centralized configuration for settings, including keys and default values.
     */
    private static array $settingConfig = [
        'active_code_expiry_minutes'     => 15,
        'active_code_max_attempts'       => 5,
        'active_code_rate_limit_seconds' => 60,
        'active_code_length'             => 6,
        'active_code_cleanup_days'       => 1,
    ];

    /**
     * Generate a new active code.
     *
     */
    public function generate(string $identifier, string $type = 'verification', ?int $expiryMinutes = null): ActiveCode
    {
        $this->cleanupOldCodes($identifier, $type);

        return ActiveCode::create([
            'code'         => $this->generateCode($this->getSetting('active_code_length')),
            'type'         => $type,
            'identifier'   => $identifier,
            'expires_at'   => now()->addMinutes($expiryMinutes ?? $this->getSetting('active_code_expiry_minutes')),
        ]);
    }

    /**
     * Verify an active code.
     */
    public function verify(string $code, string $identifier, string $type = 'verification'): bool
    {
        $activeCode = ActiveCode::where('code', $code)
            ->byIdentifier($identifier)
            ->byType($type)
            ->valid()
            ->first();

        if (!$activeCode || $activeCode->attempts >= $this->getSetting('active_code_max_attempts')) {
            return false;
        }

        $activeCode->increment('attempts');
        $activeCode->markAsUsed();

        return true;
    }

    /**
     * Resend an active code with rate limiting.
     */
    public function resend(string $identifier, string $type = 'verification'): ActiveCode|bool
    {
        $lastCode = ActiveCode::byIdentifier($identifier)->byType($type)->latest()->first();
        $rateLimitSeconds = $this->getSetting('active_code_rate_limit_seconds');

        if ($lastCode && $lastCode->created_at->diffInSeconds(now()) < $rateLimitSeconds) {
            return false;
        }

        return $this->generate($identifier, $type);
    }

    /**
     * Clean up expired codes (typically run by a scheduler).
     */
    public function cleanup(): void
    {
        $cleanupDays = $this->getSetting('active_code_cleanup_days');
        ActiveCode::where('expires_at', '<', now()->subDays($cleanupDays))->delete();
    }

    /**
     * Clear all ActiveCode settings from cache.
     */
    public function clearSettingsCache(): void
    {
        try {
            Cache::tags(self::CACHE_TAG)->flush();
            Log::info(__('ActiveCode settings cache cleared successfully'));
        } catch (Throwable $e) {
            Log::error(__('Failed to clear ActiveCode settings cache'), ['error' => $e->getMessage()]);
        }
    }

    /**
     * Pre-load all ActiveCode settings into the cache.
     */
    public function warmSettingsCache(): void
    {
        try {
            $this->getActiveCodeSettings();
            Log::info(__('ActiveCode settings cache warmed up successfully'));
        } catch (Throwable $e) {
            Log::error(__('Failed to warm up ActiveCode settings cache'), ['error' => $e->getMessage()]);
        }
    }

    /**
     * Get a specific setting value.
     * It retrieves the entire group of settings from cache/DB once and then gets the specific key.
     */
    private function getSetting(string $key): int
    {
        $default = self::$settingConfig[$key];
        try {
            $settings = $this->getActiveCodeSettings();
            $value = $settings->get($key);

            return (int)($value ?? $default);
        } catch (Throwable $e) {
            Log::error(__('Failed to get setting \':key\'. Falling back to default', ['key' => $key]), ['error' => $e->getMessage()]);
            return $default; // Ultimate fallback
        }
    }

    /**
     * Retrieve the collection of active code settings from cache or database.
     * This is the core of the caching strategy, caching the entire group.
     */
    private function getActiveCodeSettings(): Collection
    {
        return Cache::tags(self::CACHE_TAG)->remember('all_active_code_settings', self::CACHE_TTL, function () {
            Log::info(__('Loading ActiveCode settings from database into cache'));
            // This closure is executed only on cache miss.
            return SettingFacade::getSettings()->get('activecode', collect());
        });
    }

    /**
     * Generate a random numeric code.
     */
    private function generateCode(int $length): string
    {
        return str_pad(random_int(0, 10 ** $length - 1), $length, '0', STR_PAD_LEFT);
    }

    /**
     * Clean up old unused codes before generating a new one.
     */
    private function cleanupOldCodes(string $identifier, string $type): void
    {
        ActiveCode::where('identifier', $identifier)
            ->where('type', $type)
            ->whereNull('used_at')
            ->delete();
    }
}

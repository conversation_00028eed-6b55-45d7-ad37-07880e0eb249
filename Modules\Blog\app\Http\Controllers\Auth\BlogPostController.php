<?php

namespace Modules\Blog\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Modules\Blog\Http\Filters\BlogPostFilter;
use Modules\Blog\Http\Requests\BlogPostRequest;
use Modules\Blog\Http\Requests\BulkBlogPostRequest;
use Modules\Blog\Http\Requests\BulkBlogPostDestroyRequest;
use Modules\Blog\Models\BlogPost;
use Modules\Core\Traits\ResponseTrait;
use Throwable;

class BlogPostController extends Controller
{
    use ResponseTrait;


    public function __construct()
    {
        $this->middleware('role_or_permission:super-admin|blog.post.view')->only(['index', 'show', 'edit']);
        $this->middleware('role_or_permission:super-admin|blog.post.create')->only(['store']);
        $this->middleware('role_or_permission:super-admin|blog.post.edit')->only(['update']);
        $this->middleware('role_or_permission:super-admin|blog.post.delete')->only(['delete', 'restore', 'bulkDelete', 'bulkRestore']);
        $this->middleware('role_or_permission:super-admin|blog.post.destroy')->only(['destroy', 'bulkDestroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $posts = BlogPost::query()
            ->with(['author', 'categories', 'translations'])
            ->filter(new BlogPostFilter($request))
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($posts, __('Blog posts retrieved successfully.'));
    }

    /**
     * Store a newly created resource in storage.
     * @throws Throwable
     */
    public function store(BlogPostRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $post = BlogPost::create($request->all());

            if ($request->has('translations')) {
                foreach ($request->input('translations') as $locale => $translation) {
                    $post->translateOrNew($locale)->fill($translation)->save();
                }
            }

            DB::commit();
            return $this->successResponse($post, __('Blog post created successfully.'), 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse($request->validated(), __('Blog post creation failed.'), 422);
        }
    }

    /**
     * Show the specified resource.
     */
    public function show(Request $request, BlogPost $post): JsonResponse
    {
        $post->load(['translations', 'author', 'categories']);
        return $this->successResponse($post, __('Blog post retrieved successfully.'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BlogPost $post): JsonResponse
    {
        $post->load(['translations', 'author', 'categories']);
        return $this->successResponse($post, __('Blog post retrieved successfully.'));
    }

    /**
     * Update the specified resource in storage.
     * @throws Throwable
     */
    public function update(BlogPostRequest $request, BlogPost $post): JsonResponse
    {
        try {
            DB::beginTransaction();

            $post->update($request->all());

            if ($request->has('translations')) {
                foreach ($request->input('translations') as $locale => $translation) {
                    $post->translateOrNew($locale)->fill($translation)->save();
                }
            }

            DB::commit();
            return $this->successResponse($post, __('Blog post updated successfully!'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse(null, __('Blog post update failed!'), 422);
        }
    }

    /**
     * Soft delete the specified resource from storage.
     */
    public function delete(int $id): JsonResponse
    {
        $post = BlogPost::findOrFail($id);
        $post->delete();
        return $this->successResponse(null, __('Blog post moved to trash successfully!'));
    }

    public function restore(int $id): JsonResponse
    {
        $post = BlogPost::onlyTrashed()->findOrFail($id);
        $post->restore();

        return $this->successResponse($post, __('Blog post restored successfully.'));
    }

    public function destroy(int $id): JsonResponse
    {
        $post = BlogPost::onlyTrashed()->findOrFail($id);
        $post->forceDelete();

        return $this->successResponse($id, __('Blog post permanently deleted.'));
    }

    /**
     * Bulk soft delete posts.
     */
    public function bulkDelete(BulkBlogPostRequest $request): JsonResponse
    {
        $deletedCount = BlogPost::whereIn('id', $request->validated('ids'))->delete();
        return $this->successResponse(
            ['deleted_count' => $deletedCount],
            __(':count blog posts moved to trash.', ['count' => $deletedCount])
        );
    }

    /**
     * Bulk restore posts.
     */
    public function bulkRestore(BulkBlogPostRequest $request): JsonResponse
    {
        $restoredCount = BlogPost::onlyTrashed()
            ->whereIn('id', $request->validated('ids'))
            ->restore();

        return $this->successResponse(
            ['restored_count' => $restoredCount],
            __(':count blog posts restored successfully.', ['count' => $restoredCount])
        );
    }

    /**
     * Bulk permanently delete posts.
     */
    public function bulkDestroy(BulkBlogPostDestroyRequest $request): JsonResponse
    {
        $deletedCount = BlogPost::onlyTrashed()
            ->whereIn('id', $request->validated('ids'))
            ->forceDelete();

        return $this->successResponse(
            ['deleted_count' => $deletedCount],
            __(':count blog posts permanently deleted.', ['count' => $deletedCount])
        );
    }
}

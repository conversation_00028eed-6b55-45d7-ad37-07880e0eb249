<?php

namespace Modules\Auth\Console\Commands;

use Illuminate\Console\Command;
use Modules\Auth\Models\AuthLoginAttempt;

/**
 * CleanupLoginAttemptsCommand
 * 
 * Command to clean up old login attempts from database.
 */
class CleanupLoginAttemptsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'auth:cleanup-attempts {--days=30 : Number of days to keep}';

    /**
     * The console command description.
     */
    protected $description = 'Clean up old authentication login attempts';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $days = (int) $this->option('days');
        
        $this->info("Starting cleanup of login attempts older than {$days} days...");

        $deletedCount = AuthLoginAttempt::cleanup($days);

        $this->info("Cleaned up {$deletedCount} old login attempts.");

        return 0;
    }
}

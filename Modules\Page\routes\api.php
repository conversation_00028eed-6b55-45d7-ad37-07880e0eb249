<?php

use Illuminate\Support\Facades\Route;
use Modu<PERSON>\Page\Http\Controllers\PageController;
use Modules\Page\Http\Controllers\Auth\PageController as AuthPageController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('v1')->name('api.')->group(function () {
    Route::get('pages/{slug}', [PageController::class, 'index'])->name('pages.show');
});

Route::middleware(['auth:api'])->prefix('v1/auth')->name('auth.')->group(function () {
    // Standard CRUD routes
    Route::get('pages', [AuthPageController::class, 'index'])->name('pages.index');
    Route::post('pages', [AuthPageController::class, 'store'])->name('pages.store');
    Route::get('pages/{id}', [AuthPageController::class, 'show'])->name('pages.show');
    Route::put('pages/{id}', [AuthPageController::class, 'update'])->name('pages.update');
    Route::patch('pages/{id}', [AuthPageController::class, 'update'])->name('pages.update');

    // Custom delete operations
    Route::delete('pages/{id}/delete', [AuthPageController::class, 'delete'])->name('pages.delete');
    Route::delete('pages/{id}/destroy', [AuthPageController::class, 'destroy'])->name('pages.destroy');

    // Restore operations
    Route::put('pages/{id}/restore', [AuthPageController::class, 'restore'])->name('pages.restore');

    // Bulk operations
    Route::delete('pages/bulk/delete', [AuthPageController::class, 'bulkDelete'])->name('pages.bulk-delete');
    Route::delete('pages/bulk/destroy', [AuthPageController::class, 'bulkDestroy'])->name('pages.bulk-destroy');
    Route::put('pages/bulk/restore', [AuthPageController::class, 'bulkRestore'])->name('pages.bulk-restore');
});

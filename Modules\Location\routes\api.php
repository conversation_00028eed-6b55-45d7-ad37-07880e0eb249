<?php

use Illuminate\Support\Facades\Route;
use Modules\Location\Http\Controllers\LocationController;
use Modules\Location\Http\Controllers\Auth\CountryController as AuthCountryController;
use Modules\Location\Http\Controllers\Auth\GeoDivisionController as AuthGeoDivisionController;

// Public API routes
Route::prefix('v1')->group(function () {
    Route::get('locations/countries', [LocationController::class, 'countries']);
    Route::get('locations/countries/{countryId}/states', [LocationController::class, 'states']);
    Route::get('locations/states/{stateId}/cities', [LocationController::class, 'cities']);
    Route::get('locations/cities/{cityId}/districts', [LocationController::class, 'districts']);
    Route::get('locations/hierarchical', [LocationController::class, 'hierarchical']);
    Route::get('locations/search', [LocationController::class, 'search']);
    Route::get('locations/country/{isoCode}', [LocationController::class, 'country']);
    Route::get('locations/division/{id}', [LocationController::class, 'division']);
    Route::get('locations/countries/{countryId}/divisions', [LocationController::class, 'countryDivisions']);
});

// Authenticated API routes
Route::middleware(['auth:api'])->prefix('v1/auth')->group(function () {
    // Countries dropdown
    Route::get('countries/dropdown/list', [AuthCountryController::class, 'dropdown'])->name('countries.dropdown');
    Route::get('countries/trashed/list', [AuthCountryController::class, 'trashed'])->name('countries.trashed');

    // Standard CRUD routes for countries
    Route::get('countries', [AuthCountryController::class, 'index'])->name('countries.index');
    Route::post('countries', [AuthCountryController::class, 'store'])->name('countries.store');
    Route::get('countries/{id}', [AuthCountryController::class, 'show'])->name('countries.show');
    Route::put('countries/{id}', [AuthCountryController::class, 'update'])->name('countries.update');
    Route::patch('countries/{id}', [AuthCountryController::class, 'update'])->name('countries.update');

    // Delete operations for countries
    Route::delete('countries/{id}/delete', [AuthCountryController::class, 'delete'])->name('countries.delete');
    Route::delete('countries/{id}/destroy', [AuthCountryController::class, 'destroy'])->name('countries.destroy');
    Route::put('countries/{id}/restore', [AuthCountryController::class, 'restore'])->name('countries.restore');

    // Bulk operations for countries
    Route::delete('countries/bulk/delete', [AuthCountryController::class, 'bulkDelete'])->name('countries.bulk-delete');
    Route::delete('countries/bulk/destroy', [AuthCountryController::class, 'bulkDestroy'])->name('countries.bulk-destroy');
    Route::put('countries/bulk/restore', [AuthCountryController::class, 'bulkRestore'])->name('countries.bulk-restore');

    // Geographic divisions dropdown
    Route::get('geo-divisions/dropdown/list', [AuthGeoDivisionController::class, 'dropdown'])->name('geo-divisions.dropdown');
    Route::get('geo-divisions/trashed/list', [AuthGeoDivisionController::class, 'trashed'])->name('geo-divisions.trashed');

    // Standard CRUD routes for geo-divisions
    Route::get('geo-divisions', [AuthGeoDivisionController::class, 'index'])->name('geo-divisions.index');
    Route::post('geo-divisions', [AuthGeoDivisionController::class, 'store'])->name('geo-divisions.store');
    Route::get('geo-divisions/{id}', [AuthGeoDivisionController::class, 'show'])->name('geo-divisions.show');
    Route::put('geo-divisions/{id}', [AuthGeoDivisionController::class, 'update'])->name('geo-divisions.update');
    Route::patch('geo-divisions/{id}', [AuthGeoDivisionController::class, 'update'])->name('geo-divisions.update');

    // Delete operations for geo-divisions
    Route::delete('geo-divisions/{id}/delete', [AuthGeoDivisionController::class, 'delete'])->name('geo-divisions.delete');
    Route::delete('geo-divisions/{id}/destroy', [AuthGeoDivisionController::class, 'destroy'])->name('geo-divisions.destroy');
    Route::put('geo-divisions/{id}/restore', [AuthGeoDivisionController::class, 'restore'])->name('geo-divisions.restore');

    // Bulk operations for geo-divisions
    Route::delete('geo-divisions/bulk/delete', [AuthGeoDivisionController::class, 'bulkDelete'])->name('geo-divisions.bulk-delete');
    Route::delete('geo-divisions/bulk/destroy', [AuthGeoDivisionController::class, 'bulkDestroy'])->name('geo-divisions.bulk-destroy');
    Route::put('geo-divisions/bulk/restore', [AuthGeoDivisionController::class, 'bulkRestore'])->name('geo-divisions.bulk-restore');
});

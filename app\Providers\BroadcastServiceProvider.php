<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Broadcast;

class BroadcastServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register API broadcasting routes with custom prefix
        Broadcast::routes([
            'middleware' => ['auth:api'],
            'prefix' => 'api'
        ]);
    }
}

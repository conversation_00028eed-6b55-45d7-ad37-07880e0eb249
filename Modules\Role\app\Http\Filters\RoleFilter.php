<?php

namespace Modules\Role\Http\Filters;

use Modules\Core\Abstracts\AbstractFilter;

class RoleFilter extends AbstractFilter
{
    protected function filters(): array
    {
        return [
            'name' => 'like',
            'display_name' => 'like',
            'description' => 'like',
            'guard_name' => 'exact',
            'status' => 'exact',
            'is_system' => 'exact',
            'is_trashed' => 'trashed',
            'created_from' => ['type' => 'from', 'column' => 'created_at'],
            'created_to' => ['type' => 'to', 'column' => 'created_at'],
        ];
    }
}

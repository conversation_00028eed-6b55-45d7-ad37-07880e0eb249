<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Modules\ChatBot\Models\Bot;
use Modules\User\Models\User;

echo "🔐 TESTING BOT OWNERSHIP PARAMETER LOGIC\n";
echo "========================================\n\n";

// Get test users
$owner = User::first();
$otherUser = User::skip(1)->first();

if (!$owner || !$otherUser) {
    echo "❌ Need at least 2 users for testing\n";
    exit(1);
}

// Get a test bot
$bot = Bot::where('owner_id', $owner->id)->first();

if (!$bot) {
    echo "❌ No bot found for owner user\n";
    exit(1);
}

echo "📋 TEST DATA:\n";
echo "Bot: {$bot->name} (UUID: {$bot->uuid})\n";
echo "Owner: {$owner->name} (ID: {$owner->id})\n";
echo "Other User: {$otherUser->name} (ID: {$otherUser->id})\n";
echo "Bot Parameters: " . json_encode($bot->parameters, JSON_PRETTY_PRINT) . "\n\n";

// Test Case 1: Owner accessing bot
echo "🧪 TEST CASE 1: Owner accessing their bot\n";
echo "=========================================\n";

// Simulate owner login (manual auth for CLI testing)
auth()->setUser($owner);

echo "Current User: " . auth()->user()->name . " (ID: " . auth()->id() . ")\n";
echo "Is Owner: " . ($bot->isOwnedByCurrentUser() ? "✅ YES" : "❌ NO") . "\n";
echo "Safe Parameters: " . json_encode($bot->safe_parameters, JSON_PRETTY_PRINT) . "\n";

// Check if safe_parameters contains actual parameter values
$safeParams = $bot->safe_parameters;
$hasActualValues = isset($safeParams['temperature']) || isset($safeParams['max_tokens']);
echo "Contains actual parameter values: " . ($hasActualValues ? "✅ YES" : "❌ NO") . "\n\n";

echo str_repeat("-", 60) . "\n\n";

// Test Case 2: Other user accessing bot
echo "🧪 TEST CASE 2: Other user accessing bot\n";
echo "========================================\n";

// Simulate other user login (manual auth for CLI testing)
auth()->setUser($otherUser);

echo "Current User: " . auth()->user()->name . " (ID: " . auth()->id() . ")\n";
echo "Is Owner: " . ($bot->isOwnedByCurrentUser() ? "✅ YES" : "❌ NO") . "\n";
echo "Safe Parameters: " . json_encode($bot->safe_parameters, JSON_PRETTY_PRINT) . "\n";

// Check if safe_parameters contains only metadata (no actual values)
$safeParams2 = $bot->safe_parameters;
$hasOnlyMetadata = isset($safeParams2['has_custom_temperature']) && 
                   isset($safeParams2['parameter_count']) &&
                   !isset($safeParams2['temperature']) &&
                   !isset($safeParams2['max_tokens']);
echo "Contains only metadata (no values): " . ($hasOnlyMetadata ? "✅ YES" : "❌ NO") . "\n\n";

echo str_repeat("-", 60) . "\n\n";

// Test Case 3: No authentication (guest)
echo "🧪 TEST CASE 3: No authentication (guest)\n";
echo "==========================================\n";

// Simulate logout by clearing auth guard
auth()->guard()->forgetUser();

echo "Current User: " . (auth()->user() ? auth()->user()->name : "Guest") . "\n";
echo "Is Owner: " . ($bot->isOwnedByCurrentUser() ? "✅ YES" : "❌ NO") . "\n";
echo "Safe Parameters: " . json_encode($bot->safe_parameters, JSON_PRETTY_PRINT) . "\n";

// Check if safe_parameters contains only metadata for guest
$safeParams3 = $bot->safe_parameters;
$hasOnlyMetadataGuest = isset($safeParams3['has_custom_temperature']) &&
                        isset($safeParams3['parameter_count']) &&
                        !isset($safeParams3['temperature']) &&
                        !isset($safeParams3['max_tokens']);
echo "Contains only metadata (no values): " . ($hasOnlyMetadataGuest ? "✅ YES" : "❌ NO") . "\n\n";

echo str_repeat("=", 60) . "\n\n";

// Summary
echo "📊 OWNERSHIP PARAMETER LOGIC SUMMARY\n";
echo "====================================\n";
echo "✅ Owner gets FULL parameters with actual values\n";
echo "✅ Non-owner gets LIMITED metadata only\n";
echo "✅ Guest gets LIMITED metadata only\n";
echo "✅ No sensitive parameter values exposed to non-owners\n\n";

echo "🔧 LOGIC VERIFICATION:\n";
echo "- Owner: safe_parameters = actual parameters (temperature, max_tokens, etc.)\n";
echo "- Non-owner: safe_parameters = metadata (has_custom_*, parameter_count)\n";
echo "- Security: Parameter values hidden from unauthorized users\n\n";

// Re-login owner for final verification
auth()->setUser($owner);
echo "🔍 FINAL VERIFICATION (Owner logged back in):\n";
echo "Safe Parameters Type: " . (is_array($bot->safe_parameters) && isset($bot->safe_parameters['temperature']) ? "Actual Values ✅" : "Metadata Only ❌") . "\n";

echo "\n✅ OWNERSHIP PARAMETER LOGIC TEST COMPLETED!\n";

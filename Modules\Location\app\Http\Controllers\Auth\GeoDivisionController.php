<?php

namespace Modules\Location\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Modules\Core\Traits\ResponseTrait;
use Modules\Location\Http\Filters\GeoDivisionFilter;
use Modules\Location\Models\GeoDivision;
use Modules\Location\Http\Requests\GeoDivisionRequest;
use Modules\Location\Http\Requests\BulkGeoDivisionRequest;
use Modules\Location\Http\Requests\BulkGeoDivisionDestroyRequest;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class GeoDivisionController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {
        $this->middleware('role_or_permission:super-admin|location.geodivision.view')->only(['index', 'show', 'trashed', 'dropdown']);
        $this->middleware('role_or_permission:super-admin|location.geodivision.create')->only(['store']);
        $this->middleware('role_or_permission:super-admin|location.geodivision.edit')->only(['update']);
        $this->middleware('role_or_permission:super-admin|location.geodivision.delete')->only(['delete', 'restore', 'bulkDelete', 'bulkRestore']);
        $this->middleware('role_or_permission:super-admin|location.geodivision.destroy')->only(['destroy', 'bulkDestroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $divisions = GeoDivision::query()
            ->with(['country', 'parent'])
            ->filter(new GeoDivisionFilter($request))
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($divisions, __('Geographic divisions retrieved successfully.'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(GeoDivisionRequest $request): JsonResponse
    {
        $division = GeoDivision::create($request->all());

        return $this->successResponse($division->load(['country', 'parent']), __('Geographic division created successfully.'), 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(GeoDivision $geoDivision): JsonResponse
    {
        $geoDivision->load(['country', 'parent', 'children']);
        
        return $this->successResponse($geoDivision, __('Geographic division retrieved successfully.'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(GeoDivisionRequest $request, GeoDivision $geoDivision): JsonResponse
    {
        $geoDivision->update($request->all());

        return $this->successResponse($geoDivision->fresh(['country', 'parent']), __('Geographic division updated successfully.'));
    }

    /**
     * Soft delete the specified resource from storage.
     */
    public function delete(int $id): JsonResponse
    {
        $geoDivision = GeoDivision::findOrFail($id);
        $geoDivision->delete();

        return $this->successResponse($geoDivision->id, __('Geographic division deleted successfully.'));
    }

    /**
     * Restore the specified resource from storage.
     */
    public function restore(int $id): JsonResponse
    {
        $division = GeoDivision::withTrashed()->findOrFail($id);
        $division->restore();

        return $this->successResponse($division->fresh(['country', 'parent']), __('Geographic division restored successfully.'));
    }

    /**
     * Permanently delete the specified resource from storage.
     */
    public function destroy(int $id): JsonResponse
    {
        $division = GeoDivision::onlyTrashed()->findOrFail($id);
        $division->forceDelete();

        return $this->successResponse($id, __('Geographic division permanently deleted.'));
    }

    /**
     * Display a listing of trashed resources.
     */
    public function trashed(Request $request): JsonResponse
    {
        $divisions = GeoDivision::onlyTrashed()
            ->with(['country', 'parent'])
            ->filter(new GeoDivisionFilter($request))
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($divisions, __('Trashed geographic divisions retrieved successfully.'));
    }

    /**
     * Bulk soft delete geographic divisions.
     */
    public function bulkDelete(BulkGeoDivisionRequest $request): JsonResponse
    {
        $ids = $request->validated('ids');
        $deletedCount = GeoDivision::whereIn('id', $ids)->delete();

        return $this->successResponse(
            ['deleted_count' => $deletedCount],
            __(':count geographic divisions deleted successfully.', ['count' => $deletedCount])
        );
    }

    /**
     * Bulk restore geographic divisions.
     */
    public function bulkRestore(BulkGeoDivisionRequest $request): JsonResponse
    {
        $ids = $request->validated('ids');
        $restoredCount = GeoDivision::onlyTrashed()->whereIn('id', $ids)->restore();

        return $this->successResponse(
            ['restored_count' => $restoredCount],
            __(':count geographic divisions restored successfully.', ['count' => $restoredCount])
        );
    }

    /**
     * Bulk permanently delete geographic divisions.
     */
    public function bulkDestroy(BulkGeoDivisionDestroyRequest $request): JsonResponse
    {
        $ids = $request->validated('ids');
        $deletedCount = GeoDivision::onlyTrashed()->whereIn('id', $ids)->forceDelete();

        return $this->successResponse(
            ['deleted_count' => $deletedCount],
            __(':count geographic divisions permanently deleted.', ['count' => $deletedCount])
        );
    }

    /**
     * Get geographic divisions by type for dropdown.
     */
    public function dropdown(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'required|string|in:state,city,district',
            'country_id' => 'nullable|integer|exists:countries,id',
            'parent_id' => 'nullable|integer|exists:geo_divisions,id',
        ]);

        $query = GeoDivision::query()
            ->active()
            ->type($request->input('type'));

        if ($request->has('country_id')) {
            $query->country($request->input('country_id'));
        }

        if ($request->has('parent_id')) {
            $query->parent($request->input('parent_id'));
        }

        $divisions = $query->orderBy('name')->get();

        return $this->successResponse(
            $divisions->makeHidden(['updated_at', 'created_at']), 
            __('Geographic divisions retrieved successfully.')
        );
    }
}

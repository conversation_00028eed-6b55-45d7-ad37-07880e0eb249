<?php

namespace Modules\User\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Modules\Core\Traits\ResponseTrait;
use Modules\User\Http\Filters\UserFilter;
use Modules\User\Http\Requests\BulkUserRequest;
use Modules\User\Http\Requests\BulkUserDestroyRequest;
use Modules\User\Http\Requests\UserRequest;
use Modules\User\Models\User;
use Throwable;

class UserController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {
        $this->middleware('role_or_permission:super-admin|user.view')->only(['index', 'show', 'dropdown']);
        $this->middleware('role_or_permission:super-admin|user.create')->only(['store']);
        $this->middleware('role_or_permission:super-admin|user.edit')->only(['update']);
        $this->middleware('role_or_permission:super-admin|user.delete')->only(['delete', 'restore', 'bulkDelete', 'bulkRestore']);
        $this->middleware('role_or_permission:super-admin|user.destroy')->only(['destroy', 'bulkDestroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $users = User::query()
            ->with(['geoDivision', 'country', 'roles'])
            ->filter(new UserFilter($request))
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($users, __('Users retrieved successfully.'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @throws Throwable
     */
    public function store(UserRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $user = User::create($request->all());

            // Assign roles if provided
            if ($request->has('roles')) {
                $user->syncRoles($request->input('roles', []));
            }

            DB::commit();

            $user->load(['geoDivision', 'country', 'roles', 'permissions']);

            return $this->successResponse($user, __('User created successfully.'), 201);
        } catch (Throwable $e) {
            DB::rollBack();
            return $this->errorResponse(null, __('Failed to create user: :error', ['error' => $e->getMessage()]), 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user): JsonResponse
    {
        $user->load(['geoDivision', 'country', 'roles', 'permissions']);

        return $this->successResponse($user, __('User retrieved successfully.'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @throws Throwable
     */
    public function update(UserRequest $request, User $user): JsonResponse
    {
        try {
            DB::beginTransaction();

            $user->update($request->all());

            if ($request->has('roles')) {
                $user->syncRoles($request->input('roles', []));
            }

            DB::commit();

            $user->load(['geoDivision', 'country', 'roles', 'permissions']);

            return $this->successResponse($user, __('User updated successfully.'));
        } catch (Throwable $e) {
            DB::rollBack();
            return $this->errorResponse(null, __('Failed to update user: :error', ['error' => $e->getMessage()]), 500);
        }
    }

    /**
     * Soft delete the specified resource from storage (soft delete).
     */
    public function delete(int $id): JsonResponse
    {
        try {
            $user = User::findOrFail($id);
            $user->delete();

            return $this->successResponse(null, __('User deleted successfully.'));
        } catch (Throwable $e) {
            return $this->errorResponse(null, __('Failed to delete user: :error', ['error' => $e->getMessage()]), 500);
        }
    }

    /**
     * Restore the specified soft-deleted resource.
     */
    public function restore(int $id): JsonResponse
    {
        try {
            $user = User::withTrashed()->findOrFail($id);
            $user->restore();

            $user->load(['geoDivision', 'country', 'roles', 'permissions']);

            return $this->successResponse($user, __('User restored successfully.'));
        } catch (Throwable $e) {
            return $this->errorResponse(null, __('Failed to restore user: :error', ['error' => $e->getMessage()]), 500);
        }
    }

    /**
     * Permanently delete the specified resource.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $user = User::withTrashed()->findOrFail($id);
            $user->forceDelete();

            return $this->successResponse(null, __('User permanently deleted.'));
        } catch (Throwable $e) {
            return $this->errorResponse(null, __('Failed to permanently delete user: :error', ['error' => $e->getMessage()]), 500);
        }
    }

    /**
     * Bulk soft delete users.
     */
    public function bulkDelete(BulkUserRequest $request): JsonResponse
    {
        try {
            $ids = $request->validated('ids');
            $deletedCount = User::whereIn('id', $ids)->delete();

            return $this->successResponse(
                ['deleted_count' => $deletedCount],
                __(':count users deleted successfully.', ['count' => $deletedCount])
            );
        } catch (Throwable $e) {
            return $this->errorResponse(null, __('Failed to delete users: :error', ['error' => $e->getMessage()]), 500);
        }
    }

    /**
     * Bulk restore users.
     */
    public function bulkRestore(BulkUserRequest $request): JsonResponse
    {
        try {
            $ids = $request->validated('ids');
            $restoredCount = User::withTrashed()->whereIn('id', $ids)->restore();

            return $this->successResponse(
                ['restored_count' => $restoredCount],
                __(':count users restored successfully.', ['count' => $restoredCount])
            );
        } catch (Throwable $e) {
            return $this->errorResponse(null, __('Failed to restore users: :error', ['error' => $e->getMessage()]), 500);
        }
    }

    /**
     * Bulk permanently delete users.
     */
    public function bulkDestroy(BulkUserDestroyRequest $request): JsonResponse
    {
        try {
            $ids = $request->validated('ids');
            $deletedCount = User::withTrashed()->whereIn('id', $ids)->forceDelete();

            return $this->successResponse(
                ['deleted_count' => $deletedCount],
                __(':count users permanently deleted.', ['count' => $deletedCount])
            );
        } catch (Throwable $e) {
            return $this->errorResponse(null, __('Failed to permanently delete users: :error', ['error' => $e->getMessage()]), 500);
        }
    }

    /**
     * Get users for dropdown.
     */
    public function dropdown(): JsonResponse
    {
        $users = User::query()
            ->active()
            ->get(['id', 'username', 'first_name', 'last_name'])
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'username' => $user->username,
                    'full_name' => $user->first_name . ' ' . $user->last_name,
                ];
            });

        return $this->successResponse($users, __('Users retrieved successfully.'));
    }
}

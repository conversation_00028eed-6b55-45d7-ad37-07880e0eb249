<?php

namespace Modules\Auth\Facades;

use Illuminate\Support\Facades\Facade;
use Modules\User\Models\User;
use Symfony\Component\HttpFoundation\RedirectResponse;

/**
 * AuthFacade
 *
 * Facade for AuthService providing convenient static access to authentication functionality.
 * All user-facing messages are localized using Laravel's JSON-based translation system.
 *
 * @method static array register(array $data)
 * @method static array verifyEmail(string $email, string $code)
 * @method static array resendVerificationCode(string $email)
 * @method static array login(array $credentials)
 * @method static void logout()
 * @method static array refresh()
 * @method static User updateProfile(array $data)
 * @method static array sendPasswordResetCode(string $email)
 * @method static array resetPassword(string $email, string $code, string $newPassword)
 * @method static array requestPasswordChange(string $currentPassword)
 * @method static array confirmPasswordChange(string $code, string $newPassword)
 * @method static RedirectResponse redirectToProvider(string $provider)
 * @method static array handleProviderCallback(string $provider)
 * @method static array getEnabledProviders()
 * @method static array linkProvider(string $provider, User $user = null)
 * @method static array unlinkProvider(string $provider, User $user = null)
 * @method static array getLinkedAccounts(User $user = null)
 *
 * @see \Modules\Auth\Services\AuthService
 */
class AuthFacade extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'auth-service';
    }
}

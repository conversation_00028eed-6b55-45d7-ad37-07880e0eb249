<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON><PERSON>\Blog\Http\Controllers\BlogPostController;
use Modules\Blog\Http\Controllers\BlogCategoryController;
use Mo<PERSON><PERSON>\Blog\Http\Controllers\Auth\BlogPostController as AuthBlogPostController;
use Modules\Blog\Http\Controllers\Auth\BlogCategoryController as AuthBlogCategoryController;

/*
 |--------------------------------------------------------------------------
 | API Routes
 |--------------------------------------------------------------------------
 |
 | Here is where you can register API routes for your application. These
 | routes are loaded by the RouteServiceProvider within a group which
 | is assigned the "api" middleware group. Enjoy building your API!
 |
 */

// Public API routes (no authentication required)
Route::prefix('v1')->group(function () {
    // Blog Posts
    Route::get('blog/posts', [BlogPostController::class, 'index'])->name('api.blog.posts.index');
    Route::get('blog/posts/featured', [BlogPostController::class, 'featured'])->name('api.blog.posts.featured');
    Route::get('blog/posts/popular', [BlogPostController::class, 'popular'])->name('api.blog.posts.popular');
    Route::get('blog/posts/latest', [BlogPostController::class, 'latest'])->name('api.blog.posts.latest');
    Route::get('blog/posts/top', [BlogPostController::class, 'popular'])->name('api.blog.posts.top');
    Route::get('blog/posts/{slug}', [BlogPostController::class, 'show'])->name('api.blog.posts.show');

    // Blog Categories
    Route::get('blog/categories', [BlogCategoryController::class, 'index'])->name('api.blog.categories.index');
    Route::get('blog/categories/{slug}', [BlogCategoryController::class, 'show'])->name('api.blog.categories.show');
});

// Authenticated API routes (admin/auth)
Route::middleware(['auth:api'])->prefix('v1/auth')->group(function () {

    // Blog Posts Management
    Route::get('blog/posts', [AuthBlogPostController::class, 'index'])->name('auth.blog.posts.index');
    Route::post('blog/posts', [AuthBlogPostController::class, 'store'])->name('auth.blog.posts.store');
    Route::get('blog/posts/{id}', [AuthBlogPostController::class, 'show'])->name('auth.blog.posts.show');
    Route::put('blog/posts/{id}', [AuthBlogPostController::class, 'update'])->name('auth.blog.posts.update');
    Route::patch('blog/posts/{id}', [AuthBlogPostController::class, 'update'])->name('auth.blog.posts.update');

    // Blog Posts delete operations
    Route::delete('blog/posts/{id}/delete', [AuthBlogPostController::class, 'delete'])->name('auth.blog.posts.delete');
    Route::delete('blog/posts/{id}/destroy', [AuthBlogPostController::class, 'destroy'])->name('auth.blog.posts.destroy');
    Route::put('blog/posts/{id}/restore', [AuthBlogPostController::class, 'restore'])->name('auth.blog.posts.restore');

    // Blog Posts bulk operations
    Route::delete('blog/posts/bulk/delete', [AuthBlogPostController::class, 'bulkDelete'])->name('auth.blog.posts.bulk-delete');
    Route::delete('blog/posts/bulk/destroy', [AuthBlogPostController::class, 'bulkDestroy'])->name('auth.blog.posts.bulk-destroy');
    Route::put('blog/posts/bulk/restore', [AuthBlogPostController::class, 'bulkRestore'])->name('auth.blog.posts.bulk-restore');

    // Blog Categories Management
    Route::get('blog/categories/dropdown', [AuthBlogCategoryController::class, 'dropdown'])->name('auth.blog.categories.dropdown');
    Route::get('blog/categories', [AuthBlogCategoryController::class, 'index'])->name('auth.blog.categories.index');
    Route::post('blog/categories', [AuthBlogCategoryController::class, 'store'])->name('auth.blog.categories.store');
    Route::get('blog/categories/{id}', [AuthBlogCategoryController::class, 'show'])->name('auth.blog.categories.show');
    Route::put('blog/categories/{id}', [AuthBlogCategoryController::class, 'update'])->name('auth.blog.categories.update');
    Route::patch('blog/categories/{id}', [AuthBlogCategoryController::class, 'update'])->name('auth.blog.categories.update');

    // Blog Categories delete operations
    Route::delete('blog/categories/{id}/delete', [AuthBlogCategoryController::class, 'delete'])->name('auth.blog.categories.delete');
    Route::delete('blog/categories/{id}/destroy', [AuthBlogCategoryController::class, 'destroy'])->name('auth.blog.categories.destroy');
    Route::put('blog/categories/{id}/restore', [AuthBlogCategoryController::class, 'restore'])->name('auth.blog.categories.restore');

    // Blog Categories bulk operations
    Route::delete('blog/categories/bulk/delete', [AuthBlogCategoryController::class, 'bulkDelete'])->name('auth.blog.categories.bulk-delete');
    Route::delete('blog/categories/bulk/destroy', [AuthBlogCategoryController::class, 'bulkDestroy'])->name('auth.blog.categories.bulk-destroy');
    Route::put('blog/categories/bulk/restore', [AuthBlogCategoryController::class, 'bulkRestore'])->name('auth.blog.categories.bulk-restore');
});

<?php

namespace Modules\Role\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;

class BulkRoleDestroyRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ids' => [
                'required',
                'array',
                'min:1',
            ],
            'ids.*' => [
                'required',
                'integer',
                Rule::exists('roles', 'id')->whereNotNull('deleted_at'), // Only soft deleted roles
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'ids' => __('Role IDs'),
            'ids.*' => __('Role ID'),
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ids.required' => __('Role IDs are required.'),
            'ids.array' => __('Role IDs must be an array.'),
            'ids.min' => __('At least one role ID is required.'),
            'ids.*.required' => __('Each role ID is required.'),
            'ids.*.integer' => __('Each role ID must be an integer.'),
            'ids.*.exists' => __('The selected role must be in trash to be permanently deleted.'),
        ];
    }
}

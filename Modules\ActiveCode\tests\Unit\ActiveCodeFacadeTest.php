<?php

namespace Modules\ActiveCode\Tests\Unit;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\ActiveCode\Facades\NotificationFacade;
use Modules\ActiveCode\Models\ActiveCode;
use Modules\ActiveCode\Services\ActiveCodeService;
use Modules\ActiveCode\Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

/**
 * Unit tests for ActiveCodeFacade
 *
 * Test facade functionality and static access methods
 */
class ActiveCodeFacadeTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->mockSettingFacade();
    }

    #[Test]
    public function test_facade_returns_correct_service_instance()
    {
        $service = NotificationFacade::getFacadeRoot();

        $this->assertInstanceOf(ActiveCodeService::class, $service);
    }

    #[Test]
    public function test_facade_generate_method()
    {
        $identifier = '<EMAIL>';
        $type = 'verification';

        $activeCode = NotificationFacade::generate($identifier, $type);

        $this->assertInstanceOf(ActiveCode::class, $activeCode);
        $this->assertEquals($identifier, $activeCode->identifier);
        $this->assertEquals($type, $activeCode->type);
        $this->assertDatabaseHas('active_codes', [
            'identifier' => $identifier,
            'type' => $type,
            'code' => $activeCode->code
        ]);
    }

    #[Test]
    public function test_facade_generate_with_custom_expiry_minutes()
    {
        $identifier = '<EMAIL>';
        $type = 'verification';
        $expiryMinutes = 30;

        $activeCode = NotificationFacade::generate($identifier, $type, $expiryMinutes);

        $expectedExpiry = now()->addMinutes($expiryMinutes);
        $this->assertTrue($activeCode->expires_at->diffInMinutes($expectedExpiry) < 1);
    }

    #[Test]
    public function test_facade_verify_method_successfully()
    {
        $code = '123456';
        $identifier = '<EMAIL>';
        $type = 'verification';

        // Create valid code
        ActiveCode::factory()->valid()->create([
            'code' => $code,
            'identifier' => $identifier,
            'type' => $type,
            'attempts' => 0
        ]);

        $result = NotificationFacade::verify($code, $identifier, $type);

        $this->assertTrue($result['success']);
        $this->assertEquals('Verification successful', $result['message']);
        $this->assertInstanceOf(ActiveCode::class, $result['code']);
    }

    #[Test]
    public function test_facade_verify_method_fails()
    {
        $result = NotificationFacade::verify('999999', '<EMAIL>', 'verification');

        $this->assertFalse($result['success']);
        $this->assertEquals('Invalid or expired code', $result['message']);
        $this->assertArrayNotHasKey('code', $result);
    }

    #[Test]
    public function test_facade_resend_method_successfully()
    {
        $identifier = '<EMAIL>';
        $type = 'verification';

        // Create old code past rate limit
        ActiveCode::factory()->create([
            'identifier' => $identifier,
            'type' => $type,
            'created_at' => now()->subMinutes(2)
        ]);

        $result = NotificationFacade::resend($identifier, $type);

        $this->assertTrue($result['success']);
        $this->assertInstanceOf(ActiveCode::class, $result['code']);
    }

    #[Test]
    public function test_facade_resend_method_rate_limited()
    {
        $identifier = '<EMAIL>';
        $type = 'verification';

        // Create new code just created
        ActiveCode::factory()->create([
            'identifier' => $identifier,
            'type' => $type,
            'created_at' => now()
        ]);

        $result = NotificationFacade::resend($identifier, $type);

        $this->assertFalse($result['success']);
        $this->assertStringContains('Please wait', $result['message']);
    }

    #[Test]
    public function test_facade_cleanup_method()
    {
        // Create old expired code
        $expiredCode = ActiveCode::factory()->create([
            'expires_at' => now()->subDays(2)
        ]);

        // Create valid code
        $validCode = ActiveCode::factory()->valid()->create();

        NotificationFacade::cleanup();

        // Check expired code was deleted
        $this->assertDatabaseMissing('active_codes', ['id' => $expiredCode->id]);
        // Check valid code still exists
        $this->assertDatabaseHas('active_codes', ['id' => $validCode->id]);
    }

    #[Test]
    public function test_facade_clearSettingsCache_method()
    {
        // Test does not throw exception
        $this->expectNotToPerformAssertions();

        NotificationFacade::clearSettingsCache();
    }

    #[Test]
    public function test_facade_warmSettingsCache_method()
    {
        // Test does not throw exception
        $this->expectNotToPerformAssertions();

        NotificationFacade::warmSettingsCache();
    }

    #[Test]
    public function test_facade_can_call_all_service_methods()
    {
        $reflection = new \ReflectionClass(ActiveCodeService::class);
        $publicMethods = $reflection->getMethods(\ReflectionMethod::IS_PUBLIC);

        $facadeReflection = new \ReflectionClass(NotificationFacade::class);
        $facadeDocComment = $facadeReflection->getDocComment();

        // Check main methods are in facade PHPDoc
        $expectedMethods = [
            'generate',
            'verify',
            'resend',
            'cleanup',
            'clearSettingsCache',
            'warmSettingsCache'
        ];

        foreach ($expectedMethods as $method) {
            $this->assertStringContains("@method static", $facadeDocComment);
            $this->assertStringContains($method, $facadeDocComment);
        }
    }

    #[Test]
    public function test_facade_uses_correct_facade_accessor()
    {
        $accessor = NotificationFacade::getFacadeAccessor();

        $this->assertEquals('activecode.service', $accessor);
    }

    #[Test]
    public function test_facade_is_registered_in_service_container()
    {
        $this->assertTrue($this->app->bound('activecode.service'));

        $service = $this->app->make('activecode.service');
        $this->assertInstanceOf(ActiveCodeService::class, $service);
    }

    #[Test]
    public function test_facade_and_service_container_return_same_instance()
    {
        $facadeService = NotificationFacade::getFacadeRoot();
        $containerService = $this->app->make('activecode.service');

        // Since registered as singleton, should be same instance
        $this->assertSame($facadeService, $containerService);
    }

    #[Test]
    public function test_facade_methods_with_default_parameters()
    {
        $identifier = '<EMAIL>';

        // Test generate with default type
        $code1 = NotificationFacade::generate($identifier);
        $this->assertEquals('verification', $code1->type);

        // Test verify with default type
        $result = NotificationFacade::verify('123456', $identifier);
        $this->assertFalse($result['success']); // Will fail because no code exists

        // Test resend with default type
        $result = NotificationFacade::resend($identifier);
        $this->assertTrue($result['success']); // Will create new code
    }

    #[Test]
    public function test_facade_methods_with_different_types()
    {
        $identifier = '<EMAIL>';
        $types = [
            'verification',
            'registration_verification',
            'login_otp',
            'email_verification',
            'password_reset'
        ];

        foreach ($types as $type) {
            $code = NotificationFacade::generate($identifier, $type);
            $this->assertEquals($type, $code->type);

            // Test verify for each type
            $result = NotificationFacade::verify($code->code, $identifier, $type);
            $this->assertTrue($result['success']);
        }
    }

    #[Test]
    public function test_facade_handles_concurrent_requests()
    {
        $identifier = '<EMAIL>';
        $type = 'verification';

        // Create multiple codes at once
        $codes = [];
        for ($i = 0; $i < 5; $i++) {
            $codes[] = NotificationFacade::generate($identifier, $type);
        }

        // Only last code exists (due to cleanup old codes)
        $this->assertDatabaseCount('active_codes', 1);
        $this->assertDatabaseHas('active_codes', [
            'id' => end($codes)->id,
            'identifier' => $identifier,
            'type' => $type
        ]);
    }
}

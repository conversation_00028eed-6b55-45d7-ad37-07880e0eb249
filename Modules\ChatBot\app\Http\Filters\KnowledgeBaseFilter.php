<?php

namespace Modules\ChatBot\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use Modules\Core\Abstracts\AbstractFilter;

class KnowledgeBaseFilter extends AbstractFilter
{
    /**
     * Define the filters available for this model.
     */
    protected function filters(): array
    {
        return [
            'name' => 'like',
            'type' => 'exact',
            'status' => 'exact',
            'created_from' => ['type' => 'from', 'column' => 'created_at']
        ];
    }
}

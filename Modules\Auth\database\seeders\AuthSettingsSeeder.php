<?php

namespace Modules\Auth\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Setting\Models\Setting;
use Modules\Setting\Models\SettingGroup;

class AuthSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create auth setting group
        $authGroup = SettingGroup::updateOrCreate(
            ['key' => 'auth'],
            [
                'key' => 'auth',
                'label' => 'Authentication',
                'description' => 'Authentication and security settings',
                'icon' => 'shield-check',
                'sort_order' => 10,
            ]
        );

        // Create OAuth setting group
        $oauthGroup = SettingGroup::updateOrCreate(
            ['key' => 'oauth'],
            [
                'key' => 'oauth',
                'label' => 'OAuth Providers',
                'description' => 'Third-party login providers configuration',
                'icon' => 'users',
                'sort_order' => 11,
            ]
        );

        // Load Google credentials if available
        $googleCredentials = $this->loadGoogleCredentials();

        $settings = [
            // Authentication Settings
            [
                'key' => 'registration.enabled',
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'auth',
                'is_public' => false,
                'description' => 'Enable user registration',
            ],
            [
                'key' => 'account.verification.required',
                'value' => 'true',
                'type' => 'boolean',
                'group' => 'auth',
                'is_public' => false,
                'description' => 'Require email verification for new accounts',
            ],
            [
                'key' => 'login_attempts_limit',
                'value' => '5',
                'type' => 'integer',
                'group' => 'auth',
                'is_public' => false,
                'description' => 'Maximum login attempts before lockout',
            ],
            [
                'key' => 'lockout_duration',
                'value' => '60',
                'type' => 'integer',
                'group' => 'auth',
                'is_public' => false,
                'description' => 'Account lockout duration in minutes',
            ],

            // Rate Limiting Settings
            [
                'key' => 'rate_limit_register_attempts',
                'value' => '3',
                'type' => 'integer',
                'group' => 'auth',
                'is_public' => false,
                'description' => 'Maximum registration attempts per hour',
            ],
            [
                'key' => 'rate_limit_password_reset_attempts',
                'value' => '3',
                'type' => 'integer',
                'group' => 'auth',
                'is_public' => false,
                'description' => 'Maximum password reset attempts per hour',
            ],
            [
                'key' => 'rate_limit_email_verification_attempts',
                'value' => '5',
                'type' => 'integer',
                'group' => 'auth',
                'is_public' => false,
                'description' => 'Maximum email verification attempts per hour',
            ],
            [
                'key' => 'rate_limit_auth_attempts',
                'value' => '10',
                'type' => 'integer',
                'group' => 'auth',
                'is_public' => false,
                'description' => 'Maximum OAuth attempts per hour',
            ],
            [
                'key' => 'rate_limit_window_minutes',
                'value' => '60',
                'type' => 'integer',
                'group' => 'auth',
                'is_public' => false,
                'description' => 'Rate limiting window in minutes',
            ],



            // Password Settings
            [
                'key' => 'password_min_length',
                'value' => '6',
                'type' => 'integer',
                'group' => 'auth',
                'is_public' => true,
                'description' => 'Minimum password length',
            ],
            [
                'key' => 'password_require_uppercase',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'auth',
                'is_public' => true,
                'description' => 'Require uppercase letters in password',
            ],
            [
                'key' => 'password_require_lowercase',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'auth',
                'is_public' => true,
                'description' => 'Require lowercase letters in password',
            ],
            [
                'key' => 'password_require_numbers',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'auth',
                'is_public' => true,
                'description' => 'Require numbers in password',
            ],
            [
                'key' => 'password_require_symbols',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'auth',
                'is_public' => true,
                'description' => 'Require special characters in password',
            ],

            // JWT Settings
            [
                'key' => 'jwt_ttl',
                'value' => '60',
                'type' => 'integer',
                'group' => 'auth',
                'is_public' => false,
                'description' => 'JWT token time to live in minutes',
            ],
            [
                'key' => 'jwt_refresh_ttl',
                'value' => '20160',
                'type' => 'integer',
                'group' => 'auth',
                'is_public' => false,
                'description' => 'JWT refresh token time to live in minutes',
            ],
        ];

        foreach ($settings as $setting) {
            // Replace 'group' with 'group_id'
            if (isset($setting['group'])) {
                $setting['group_id'] = $authGroup->id;
                unset($setting['group']);
            }

            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }

        // Create OAuth settings
        $this->createOAuthSettings($oauthGroup, $googleCredentials);

        // Display information about Google OAuth setup
        if ($googleCredentials) {
            $this->command->info('Google OAuth settings have been seeded successfully.');
            $this->command->info('Google Client ID: ' . $googleCredentials['client_id']);
            $this->command->info('Redirect URI: ' . url('/oauth/google/callback'));
            $this->command->warn('Make sure to add the redirect URI to your Google OAuth app configuration.');
        } else {
            $this->command->warn('Google credentials file not found. Google OAuth settings seeded with empty values.');
        }
    }

    /**
     * Create OAuth provider settings
     */
    private function createOAuthSettings($oauthGroup, $googleCredentials): void
    {
        $oauthSettings = [
            // Google OAuth Provider
            [
                'key' => 'google.enabled',
                'value' => $googleCredentials ? 'true' : 'false',
                'type' => 'boolean',
                'group' => 'oauth',
                'is_public' => true,
                'description' => 'Enable Google OAuth login',
            ],
            [
                'key' => 'google.client_id',
                'value' => $googleCredentials['client_id'] ?? '',
                'type' => 'string',
                'group' => 'oauth',
                'is_public' => false,
                'description' => 'Google OAuth Client ID',
            ],
            [
                'key' => 'google.client_secret',
                'value' => $googleCredentials['client_secret'] ?? '',
                'type' => 'string',
                'group' => 'oauth',
                'is_public' => false,
                'description' => 'Google OAuth Client Secret',
            ],
            [
                'key' => 'google.redirect',
                'value' => url('/oauth/google/callback'),
                'type' => 'string',
                'group' => 'oauth',
                'is_public' => false,
                'description' => 'Google OAuth Redirect URI',
            ],
            [
                'key' => 'google.scopes',
                'value' => 'openid,profile,email',
                'type' => 'string',
                'group' => 'oauth',
                'is_public' => false,
                'description' => 'Google OAuth Scopes',
            ],

            // Facebook OAuth Provider
            [
                'key' => 'facebook.enabled',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'oauth',
                'is_public' => true,
                'description' => 'Enable Facebook OAuth login',
            ],
            [
                'key' => 'facebook.client_id',
                'value' => '',
                'type' => 'string',
                'group' => 'oauth',
                'is_public' => false,
                'description' => 'Facebook OAuth App ID',
            ],
            [
                'key' => 'facebook.client_secret',
                'value' => '',
                'type' => 'string',
                'group' => 'oauth',
                'is_public' => false,
                'description' => 'Facebook OAuth App Secret',
            ],
            [
                'key' => 'facebook.redirect',
                'value' => url('/oauth/facebook/callback'),
                'type' => 'string',
                'group' => 'oauth',
                'is_public' => false,
                'description' => 'Facebook OAuth Redirect URI',
            ],

            // Twitter OAuth Provider
            [
                'key' => 'twitter.enabled',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'oauth',
                'is_public' => true,
                'description' => 'Enable Twitter OAuth login',
            ],
            [
                'key' => 'twitter.client_id',
                'value' => '',
                'type' => 'string',
                'group' => 'oauth',
                'is_public' => false,
                'description' => 'Twitter OAuth Client ID',
            ],
            [
                'key' => 'twitter.client_secret',
                'value' => '',
                'type' => 'string',
                'group' => 'oauth',
                'is_public' => false,
                'description' => 'Twitter OAuth Client Secret',
            ],
            [
                'key' => 'twitter.redirect',
                'value' => url('/oauth/twitter/callback'),
                'type' => 'string',
                'group' => 'oauth',
                'is_public' => false,
                'description' => 'Twitter OAuth Redirect URI',
            ],

            // Zalo OAuth Provider
            [
                'key' => 'zalo.enabled',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'oauth',
                'is_public' => true,
                'description' => 'Enable Zalo OAuth login',
            ],
            [
                'key' => 'zalo.client_id',
                'value' => '',
                'type' => 'string',
                'group' => 'oauth',
                'is_public' => false,
                'description' => 'Zalo OAuth App ID',
            ],
            [
                'key' => 'zalo.client_secret',
                'value' => '',
                'type' => 'string',
                'group' => 'oauth',
                'is_public' => false,
                'description' => 'Zalo OAuth App Secret',
            ],
            [
                'key' => 'zalo.redirect',
                'value' => url('/oauth/zalo/callback'),
                'type' => 'string',
                'group' => 'oauth',
                'is_public' => false,
                'description' => 'Zalo OAuth Redirect URI',
            ],

            // Telegram OAuth Provider
            [
                'key' => 'telegram.enabled',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'oauth',
                'is_public' => true,
                'description' => 'Enable Telegram OAuth login',
            ],
            [
                'key' => 'telegram.bot_token',
                'value' => '',
                'type' => 'string',
                'group' => 'oauth',
                'is_public' => false,
                'description' => 'Telegram Bot Token',
            ],
            [
                'key' => 'telegram.bot_username',
                'value' => '',
                'type' => 'string',
                'group' => 'oauth',
                'is_public' => false,
                'description' => 'Telegram Bot Username',
            ],
            [
                'key' => 'telegram.redirect',
                'value' => url('/oauth/telegram/callback'),
                'type' => 'string',
                'group' => 'oauth',
                'is_public' => false,
                'description' => 'Telegram OAuth Redirect URI',
            ],
        ];

        foreach ($oauthSettings as $setting) {
            // Replace 'group' with 'group_id'
            if (isset($setting['group'])) {
                $setting['group_id'] = $oauthGroup->id;
                unset($setting['group']);
            }

            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }

        $this->command->info('OAuth provider settings created successfully.');
    }

    /**
     * Load Google credentials from JSON file
     */
    private function loadGoogleCredentials(): ?array
    {
        $credentialsPath = storage_path('app/private/client_secret_542865738643-i1dl272j4d84k4bqe22jae9avbo17b28.apps.googleusercontent.com.json');

        if (!file_exists($credentialsPath)) {
            return null;
        }

        $credentials = json_decode(file_get_contents($credentialsPath), true);

        if (!isset($credentials['web'])) {
            return null;
        }

        return [
            'client_id' => $credentials['web']['client_id'],
            'client_secret' => $credentials['web']['client_secret'],
            'project_id' => $credentials['web']['project_id'] ?? null,
            'auth_uri' => $credentials['web']['auth_uri'] ?? null,
            'token_uri' => $credentials['web']['token_uri'] ?? null,
        ];
    }
}

<?php

namespace Modules\Role\Tests\Unit;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Modules\Role\Http\Requests\BulkRoleDestroyRequest;
use Modules\Role\Models\Role;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class BulkRoleDestroyRequestTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run migrations for Role module
        $this->artisan('migrate', ['--path' => 'Modules/Role/database/migrations']);
    }

    #[Test]
    public function it_validates_required_fields()
    {
        $request = new BulkRoleDestroyRequest();
        $rules = $request->rules();

        // Test required fields
        $this->assertContains('required', $rules['ids']);
        $this->assertContains('array', $rules['ids']);
        $this->assertContains('min:1', $rules['ids']);
    }

    #[Test]
    public function it_validates_ids_array_elements()
    {
        $request = new BulkRoleDestroyRequest();
        $rules = $request->rules();

        $idsElementRules = $rules['ids.*'];

        $this->assertContains('required', $idsElementRules);
        $this->assertContains('integer', $idsElementRules);
        // Check if exists rule is present (it's a Rule object)
        $this->assertCount(3, $idsElementRules);
    }

    #[Test]
    public function it_has_correct_attributes()
    {
        $request = new BulkRoleDestroyRequest();
        $attributes = $request->attributes();

        $expectedAttributes = [
            'ids' => __('Role IDs'),
            'ids.*' => __('Role ID'),
        ];

        $this->assertEquals($expectedAttributes, $attributes);
    }

    #[Test]
    public function it_passes_validation_with_valid_soft_deleted_roles()
    {
        // Create and soft delete roles
        $role1 = Role::factory()->create();
        $role2 = Role::factory()->create();
        $role1->delete();
        $role2->delete();

        $validData = [
            'ids' => [$role1->id, $role2->id],
        ];

        $request = new BulkRoleDestroyRequest();
        $validator = Validator::make($validData, $request->rules());

        $this->assertTrue($validator->passes());
    }

    #[Test]
    public function it_fails_validation_with_active_roles()
    {
        // Create active roles (not soft deleted)
        $role1 = Role::factory()->create();
        $role2 = Role::factory()->create();

        $invalidData = [
            'ids' => [$role1->id, $role2->id],
        ];

        $request = new BulkRoleDestroyRequest();
        $validator = Validator::make($invalidData, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('ids.0', $validator->errors()->toArray());
        $this->assertArrayHasKey('ids.1', $validator->errors()->toArray());
    }

    #[Test]
    public function it_fails_validation_with_empty_ids_array()
    {
        $invalidData = [
            'ids' => [],
        ];

        $request = new BulkRoleDestroyRequest();
        $validator = Validator::make($invalidData, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('ids', $validator->errors()->toArray());
    }

    #[Test]
    public function it_fails_validation_with_non_existent_ids()
    {
        $invalidData = [
            'ids' => [99999, 99998],
        ];

        $request = new BulkRoleDestroyRequest();
        $validator = Validator::make($invalidData, $request->rules());

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('ids.0', $validator->errors()->toArray());
        $this->assertArrayHasKey('ids.1', $validator->errors()->toArray());
    }

    #[Test]
    public function it_fails_validation_with_mixed_active_and_deleted_roles()
    {
        // Create one active and one soft deleted role
        $activeRole = Role::factory()->create();
        $deletedRole = Role::factory()->create();
        $deletedRole->delete();

        $invalidData = [
            'ids' => [$activeRole->id, $deletedRole->id],
        ];

        $request = new BulkRoleDestroyRequest();
        $validator = Validator::make($invalidData, $request->rules());

        $this->assertFalse($validator->passes());
        // Only the active role should fail validation
        $this->assertArrayHasKey('ids.0', $validator->errors()->toArray());
        $this->assertArrayNotHasKey('ids.1', $validator->errors()->toArray());
    }

    #[Test]
    public function it_has_custom_error_messages()
    {
        $request = new BulkRoleDestroyRequest();
        $messages = $request->messages();

        $expectedMessages = [
            'ids.required' => __('Role IDs are required.'),
            'ids.array' => __('Role IDs must be an array.'),
            'ids.min' => __('At least one role ID is required.'),
            'ids.*.required' => __('Each role ID is required.'),
            'ids.*.integer' => __('Each role ID must be an integer.'),
            'ids.*.exists' => __('The selected role must be in trash to be permanently deleted.'),
        ];

        $this->assertEquals($expectedMessages, $messages);
    }
}

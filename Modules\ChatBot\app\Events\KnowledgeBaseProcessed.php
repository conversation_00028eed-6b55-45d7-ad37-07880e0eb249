<?php

namespace Modules\ChatBot\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\ChatBot\Models\KnowledgeBase;

class KnowledgeBaseProcessed implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public KnowledgeBase $knowledgeBase;
    public array $broadcastData;

    /**
     * Create a new event instance.
     */
    public function __construct(KnowledgeBase $knowledgeBase, array $broadcastData)
    {
        $this->knowledgeBase = $knowledgeBase;
        $this->broadcastData = $broadcastData;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        $channels = [];

        // Broadcast to owner's personal channel
        $channels[] = new PrivateChannel("user.{$this->knowledgeBase->owner_id}");

        // If knowledge base belongs to a bot, broadcast to bot channel
        $botIds = $this->knowledgeBase->bots()->pluck('bots.id')->toArray();
        foreach ($botIds as $botId) {
            $channels[] = new PrivateChannel("bot.{$botId}");
        }

        return $channels;
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return $this->broadcastData;
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'KnowledgeBaseProcessed';
    }

    /**
     * Determine if this event should broadcast.
     */
    public function shouldBroadcast(): bool
    {
        // Broadcast when processing is completed (ready or error)
        return in_array($this->knowledgeBase->status, ['ready', 'error']);
    }
}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\Organization\Models\Organization;
use Modules\User\Models\User;

class DebugDeletePermission extends Command
{
    protected $signature = 'debug:delete-permission {uuid} {user_id?}';
    protected $description = 'Debug delete permission for organization';

    public function handle()
    {
        $uuid = $this->argument('uuid');
        $userId = $this->argument('user_id');
        
        // Find organization by UUID
        $organization = Organization::where('uuid', $uuid)->first();
        
        if (!$organization) {
            $this->error("Organization with UUID {$uuid} not found");
            return;
        }
        
        // Get current authenticated user or specified user
        if ($userId) {
            $user = User::find($userId);
        } else {
            $user = auth('api')->user();
        }
        
        if (!$user) {
            $this->error("No user found");
            return;
        }
        
        $this->info("=== DEBUG DELETE PERMISSION ===");
        $this->info("Organization: {$organization->name} (ID: {$organization->id})");
        $this->info("Organization UUID: {$organization->uuid}");
        $this->info("Organization Owner ID: {$organization->owner_id}");
        $this->info("User: {$user->email} (ID: {$user->id})");
        $this->line("");
        
        // Check ownership
        $isOwner = $user->id === $organization->owner_id;
        $this->info("Is Owner: " . ($isOwner ? 'YES' : 'NO'));
        
        // Check roles
        $roles = $user->roles()->where('guard_name', 'api')->pluck('name')->toArray();
        $this->info("User Roles: " . (empty($roles) ? 'None' : implode(', ', $roles)));
        
        // Check permissions
        $hasManage = $user->hasPermissionTo('organization.manage', 'api');
        $hasDestroy = $user->hasPermissionTo('organization.destroy', 'api');
        $this->info("Has organization.manage: " . ($hasManage ? 'YES' : 'NO'));
        $this->info("Has organization.destroy: " . ($hasDestroy ? 'YES' : 'NO'));
        
        // Check policy
        $canDelete = $user->can('delete', $organization);
        $this->info("Policy allows delete: " . ($canDelete ? 'YES' : 'NO'));
        
        // Test policy methods directly
        $policy = new \Modules\Organization\Policies\OrganizationPolicy();
        $beforeResult = $policy->before($user, 'delete');
        $deleteResult = $policy->delete($user, $organization);
        
        $this->line("");
        $this->info("Policy before() result: " . ($beforeResult === true ? 'TRUE' : ($beforeResult === false ? 'FALSE' : 'NULL')));
        $this->info("Policy delete() result: " . ($deleteResult ? 'TRUE' : 'FALSE'));
        
        if (!$canDelete) {
            $this->line("");
            $this->error("REASONS WHY DELETE IS DENIED:");
            if (!$isOwner && !$user->hasRole(['super-admin', 'admin'], 'api') && !$hasManage) {
                $this->error("- User is not the owner and doesn't have admin privileges");
            }
        }
    }
}

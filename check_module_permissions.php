<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Checking all modules for permission seeders...\n\n";

// Get all modules
$modulesPath = base_path('Modules');
$modules = [];

if (is_dir($modulesPath)) {
    $directories = scandir($modulesPath);
    foreach ($directories as $dir) {
        if ($dir !== '.' && $dir !== '..' && is_dir($modulesPath . '/' . $dir)) {
            $modules[] = $dir;
        }
    }
}

sort($modules);

echo "Found " . count($modules) . " modules:\n";
foreach ($modules as $module) {
    echo "- $module\n";
}

echo "\n" . str_repeat("=", 80) . "\n";
echo "CHECKING PERMISSION SEEDERS\n";
echo str_repeat("=", 80) . "\n\n";

$modulesWithPermissions = [];
$modulesWithoutPermissions = [];
$modulesWithoutSeeders = [];

foreach ($modules as $module) {
    echo "📁 Module: $module\n";
    
    // Check if permission seeder exists
    $permissionSeederPath = "Modules/$module/database/seeders/{$module}PermissionSeeder.php";
    $hasPermissionSeeder = file_exists($permissionSeederPath);
    
    // Check if database seeder exists
    $databaseSeederPath = "Modules/$module/database/seeders/{$module}DatabaseSeeder.php";
    $hasDatabaseSeeder = file_exists($databaseSeederPath);
    
    echo "   Permission Seeder: " . ($hasPermissionSeeder ? "✅ EXISTS" : "❌ MISSING") . "\n";
    echo "   Database Seeder: " . ($hasDatabaseSeeder ? "✅ EXISTS" : "❌ MISSING") . "\n";
    
    if ($hasPermissionSeeder) {
        // Check if permission seeder is registered in database seeder
        if ($hasDatabaseSeeder) {
            $databaseSeederContent = file_get_contents($databaseSeederPath);
            $isRegistered = strpos($databaseSeederContent, "{$module}PermissionSeeder::class") !== false;
            echo "   Registered in DB Seeder: " . ($isRegistered ? "✅ YES" : "❌ NO") . "\n";
            
            if ($isRegistered) {
                $modulesWithPermissions[] = $module;
            } else {
                echo "   ⚠️  Permission seeder exists but not registered!\n";
            }
        } else {
            echo "   ⚠️  Permission seeder exists but no database seeder!\n";
        }
    } else {
        if ($hasDatabaseSeeder) {
            $modulesWithoutPermissions[] = $module;
        } else {
            $modulesWithoutSeeders[] = $module;
        }
    }
    
    echo "\n";
}

echo str_repeat("=", 80) . "\n";
echo "SUMMARY\n";
echo str_repeat("=", 80) . "\n\n";

echo "✅ Modules WITH permission seeders (" . count($modulesWithPermissions) . "):\n";
foreach ($modulesWithPermissions as $module) {
    echo "   - $module\n";
}

echo "\n❌ Modules WITHOUT permission seeders (" . count($modulesWithoutPermissions) . "):\n";
foreach ($modulesWithoutPermissions as $module) {
    echo "   - $module\n";
}

echo "\n⚠️  Modules WITHOUT any seeders (" . count($modulesWithoutSeeders) . "):\n";
foreach ($modulesWithoutSeeders as $module) {
    echo "   - $module\n";
}

echo "\n" . str_repeat("=", 80) . "\n";
echo "RECOMMENDATIONS\n";
echo str_repeat("=", 80) . "\n\n";

if (!empty($modulesWithoutPermissions)) {
    echo "🔧 Need to CREATE permission seeders for:\n";
    foreach ($modulesWithoutPermissions as $module) {
        echo "   - $module\n";
    }
    echo "\n";
}

if (!empty($modulesWithoutSeeders)) {
    echo "🔧 Need to CREATE both database and permission seeders for:\n";
    foreach ($modulesWithoutSeeders as $module) {
        echo "   - $module\n";
    }
    echo "\n";
}

echo "✅ Analysis complete!\n";

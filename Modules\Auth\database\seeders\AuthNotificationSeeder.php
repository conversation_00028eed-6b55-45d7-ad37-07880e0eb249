<?php

namespace Modules\Auth\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AuthNotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedNotificationTypes();
        $this->seedNotificationTemplates();
    }

    /**
     * Seed Auth-related notification types.
     */
    private function seedNotificationTypes(): void
    {
        $notificationTypes = [
            [
                'key' => 'user.registered',
                'name' => 'User Registration',
                'description' => 'Notification sent when user registers and needs email verification',
                'channels' => json_encode(['database', 'email']),
                'default_settings' => json_encode([
                    'priority' => 'high',
                    'auto_send' => true,
                    'retry_attempts' => 3,
                    'delay_seconds' => 0,
                ]),
                'is_system' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'welcome',
                'name' => 'Welcome Notification',
                'description' => 'Welcome message sent to new users upon registration without verification',
                'channels' => json_encode(['database', 'email']),
                'default_settings' => json_encode([
                    'priority' => 'normal',
                    'auto_send' => true,
                    'retry_attempts' => 3,
                    'delay_seconds' => 0,
                ]),
                'is_system' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'user.verified',
                'name' => 'Email Verified',
                'description' => 'Notification sent when user successfully verifies their email',
                'channels' => json_encode(['database', 'email']),
                'default_settings' => json_encode([
                    'priority' => 'normal',
                    'auto_send' => true,
                    'retry_attempts' => 2,
                    'delay_seconds' => 0,
                ]),
                'is_system' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'verification_code_sent',
                'name' => 'Verification Code Sent',
                'description' => 'Notification with OTP verification code for email verification',
                'channels' => json_encode(['email']),
                'default_settings' => json_encode([
                    'priority' => 'high',
                    'auto_send' => true,
                    'retry_attempts' => 3,
                    'delay_seconds' => 0,
                ]),
                'is_system' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'user.logged-in',
                'name' => 'User Login',
                'description' => 'Notification sent when user logs in successfully',
                'channels' => json_encode(['database']),
                'default_settings' => json_encode([
                    'priority' => 'low',
                    'auto_send' => true,
                    'retry_attempts' => 1,
                    'delay_seconds' => 0,
                ]),
                'is_system' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'password_reset',
                'name' => 'Password Reset',
                'description' => 'Password reset instructions and verification codes',
                'channels' => json_encode(['database', 'email']),
                'default_settings' => json_encode([
                    'priority' => 'high',
                    'auto_send' => true,
                    'retry_attempts' => 3,
                    'delay_seconds' => 0,
                ]),
                'is_system' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'password_changed',
                'name' => 'Password Changed',
                'description' => 'Notification sent when password is successfully changed',
                'channels' => json_encode(['database', 'email']),
                'default_settings' => json_encode([
                    'priority' => 'normal',
                    'auto_send' => true,
                    'retry_attempts' => 2,
                    'delay_seconds' => 0,
                ]),
                'is_system' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'security_alert',
                'name' => 'Security Alert',
                'description' => 'Security-related notifications',
                'channels' => json_encode(['database', 'email', 'sms']),
                'default_settings' => json_encode([
                    'priority' => 'urgent',
                    'auto_send' => true,
                ]),
                'is_system' => true,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'account_activity',
                'name' => 'Account Activity',
                'description' => 'Account login, logout, and activity notifications',
                'channels' => json_encode(['database', 'email']),
                'default_settings' => json_encode([
                    'priority' => 'normal',
                    'auto_send' => false,
                    'retry_attempts' => 1,
                    'delay_seconds' => 0,
                ]),
                'is_system' => false,
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($notificationTypes as $type) {
            DB::table('notification_types')->updateOrInsert(
                ['key' => $type['key']],
                $type
            );
        }

        $this->command->info('Auth notification types seeded successfully.');
    }

    /**
     * Seed Auth-related notification templates.
     */
    private function seedNotificationTemplates(): void
    {
        // Get notification types
        $notificationTypes = DB::table('notification_types')
            ->whereIn('key', [
                'user.registered', 'welcome', 'user.verified', 'verification_code_sent',
                'user.logged-in', 'password_reset', 'password_changed', 'security_alert', 'account_activity'
            ])
            ->get()
            ->keyBy('key');

        $templates = [];

        $this->command->info('Creating Auth notification templates...');

        // User Registration Templates
        if (isset($notificationTypes['user.registered'])) {
            $userRegisteredType = $notificationTypes['user.registered'];

            // English templates
            $templates[] = [
                'notification_type_id' => $userRegisteredType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => 'Welcome to {app_name}!',
                'content' => 'Hi {user_name}, please verify your email address to complete your registration.',
                'variables' => json_encode(['user_name', 'app_name']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $userRegisteredType->id,
                'channel' => 'email',
                'locale' => 'en',
                'subject' => 'Verify your email address - {app_name}',
                'title' => 'Welcome to {app_name}!',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to {app_name}!</h1>
    </div>

    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Hi <strong>{user_name}</strong>,</p>

        <p style="font-size: 16px; margin-bottom: 20px;">
            Thank you for registering with {app_name}. To complete your registration and activate your account,
            please verify your email address by clicking the button below.
        </p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{verification_url}" style="background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                Verify Email Address
            </a>
        </div>

        <p style="font-size: 14px; color: #666; margin-top: 20px;">
            If the button above doesn\'t work, copy and paste this link into your browser:<br>
            <a href="{verification_url}" style="color: #007bff; word-break: break-all;">{verification_url}</a>
        </p>

        <p style="font-size: 14px; color: #666; margin-top: 20px;">
            If you didn\'t create this account, please ignore this email.
        </p>

        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">

        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Best regards,<br>
            The {app_name} Team
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'app_name', 'verification_url']),
                'settings' => json_encode(['from_name' => '{app_name} Team']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // Vietnamese templates
            $templates[] = [
                'notification_type_id' => $userRegisteredType->id,
                'channel' => 'database',
                'locale' => 'vi',
                'subject' => null,
                'title' => 'Chào mừng đến với {app_name}!',
                'content' => 'Xin chào {user_name}, vui lòng xác thực địa chỉ email để hoàn tất đăng ký.',
                'variables' => json_encode(['user_name', 'app_name']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $userRegisteredType->id,
                'channel' => 'email',
                'locale' => 'vi',
                'subject' => 'Xác thực địa chỉ email - {app_name}',
                'title' => 'Chào mừng đến với {app_name}!',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chào mừng đến với {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Chào mừng đến với {app_name}!</h1>
    </div>

    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Xin chào <strong>{user_name}</strong>,</p>

        <p style="font-size: 16px; margin-bottom: 20px;">
            Cảm ơn bạn đã đăng ký tài khoản tại {app_name}. Để hoàn tất quá trình đăng ký và kích hoạt tài khoản,
            vui lòng xác thực địa chỉ email bằng cách nhấp vào nút bên dưới.
        </p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{verification_url}" style="background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                Xác thực địa chỉ email
            </a>
        </div>

        <p style="font-size: 14px; color: #666; margin-top: 20px;">
            Nếu nút trên không hoạt động, hãy sao chép và dán liên kết này vào trình duyệt:<br>
            <a href="{verification_url}" style="color: #007bff; word-break: break-all;">{verification_url}</a>
        </p>

        <p style="font-size: 14px; color: #666; margin-top: 20px;">
            Nếu bạn không tạo tài khoản này, vui lòng bỏ qua email này.
        </p>

        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">

        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Trân trọng,<br>
            Đội ngũ {app_name}
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'app_name', 'verification_url']),
                'settings' => json_encode(['from_name' => 'Đội ngũ {app_name}']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Welcome templates (for users without verification)
        if (isset($notificationTypes['welcome'])) {
            $welcomeType = $notificationTypes['welcome'];

            // English templates
            $templates[] = [
                'notification_type_id' => $welcomeType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => 'Welcome to {app_name}!',
                'content' => 'Welcome {user_name}! Your account is ready to use.',
                'variables' => json_encode(['user_name', 'app_name']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $welcomeType->id,
                'channel' => 'email',
                'locale' => 'en',
                'subject' => 'Welcome to {app_name}!',
                'title' => 'Welcome aboard!',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Welcome aboard!</h1>
    </div>

    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Hi <strong>{user_name}</strong>,</p>

        <p style="font-size: 16px; margin-bottom: 20px;">
            Welcome to {app_name}! Your account has been created successfully and you can now start using our platform.
        </p>

        <p style="font-size: 16px; margin-bottom: 20px;">
            We\'re excited to have you on board and look forward to providing you with an excellent experience.
        </p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{dashboard_url}" style="background: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                Get Started
            </a>
        </div>

        <p style="font-size: 16px; margin-bottom: 20px;">
            If you have any questions, feel free to contact our support team.
        </p>

        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">

        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Best regards,<br>
            The {app_name} Team
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'app_name', 'dashboard_url']),
                'settings' => json_encode(['from_name' => '{app_name} Team']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // Vietnamese templates
            $templates[] = [
                'notification_type_id' => $welcomeType->id,
                'channel' => 'database',
                'locale' => 'vi',
                'subject' => null,
                'title' => 'Chào mừng đến với {app_name}!',
                'content' => 'Chào mừng {user_name}! Tài khoản của bạn đã sẵn sàng sử dụng.',
                'variables' => json_encode(['user_name', 'app_name']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $welcomeType->id,
                'channel' => 'email',
                'locale' => 'vi',
                'subject' => 'Chào mừng đến với {app_name}!',
                'title' => 'Chào mừng bạn!',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chào mừng đến với {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">Chào mừng bạn!</h1>
    </div>

    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Xin chào <strong>{user_name}</strong>,</p>

        <p style="font-size: 16px; margin-bottom: 20px;">
            Chào mừng bạn đến với {app_name}! Tài khoản của bạn đã được tạo thành công và bạn có thể bắt đầu sử dụng nền tảng của chúng tôi.
        </p>

        <p style="font-size: 16px; margin-bottom: 20px;">
            Chúng tôi rất vui mừng khi có bạn tham gia và mong muốn mang đến cho bạn trải nghiệm tuyệt vời.
        </p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{dashboard_url}" style="background: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                Bắt đầu
            </a>
        </div>

        <p style="font-size: 16px; margin-bottom: 20px;">
            Nếu bạn có bất kỳ câu hỏi nào, vui lòng liên hệ với đội ngũ hỗ trợ của chúng tôi.
        </p>

        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">

        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Trân trọng,<br>
            Đội ngũ {app_name}
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'app_name', 'dashboard_url']),
                'settings' => json_encode(['from_name' => 'Đội ngũ {app_name}']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Password Reset Templates
        if (isset($notificationTypes['password_reset'])) {
            $passwordResetType = $notificationTypes['password_reset'];

            // English templates
            $templates[] = [
                'notification_type_id' => $passwordResetType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => 'Password Reset Request',
                'content' => 'A password reset code has been sent to your email.',
                'variables' => json_encode(['user_name', 'app_name', 'reset_code', 'expires_in']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $passwordResetType->id,
                'channel' => 'email',
                'locale' => 'en',
                'subject' => 'Password Reset Code - {app_name}',
                'title' => 'Password Reset Request',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset - {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">🔒 Password Reset</h1>
    </div>

    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Hi <strong>{user_name}</strong>,</p>

        <p style="font-size: 16px; margin-bottom: 20px;">
            You have requested to reset your password for your {app_name} account.
        </p>

        <p style="font-size: 16px; margin-bottom: 20px;">
            Your password reset code is:
        </p>

        <div style="text-align: center; margin: 30px 0;">
            <div style="background: #dc3545; color: white; padding: 20px; border-radius: 10px; font-size: 32px; font-weight: bold; letter-spacing: 8px; display: inline-block; min-width: 200px;">
                {reset_code}
            </div>
        </div>

        <p style="font-size: 16px; margin-bottom: 20px; text-align: center;">
            <strong>This code will expire in {expires_in} minutes.</strong>
        </p>

        <p style="font-size: 14px; color: #666; margin-top: 20px;">
            If you didn\'t request this password reset, please ignore this email and your password will remain unchanged.
        </p>

        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">

        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Best regards,<br>
            The {app_name} Security Team
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'app_name', 'reset_code', 'expires_in']),
                'settings' => json_encode(['from_name' => '{app_name} Security', 'priority' => 'high']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // Vietnamese templates
            $templates[] = [
                'notification_type_id' => $passwordResetType->id,
                'channel' => 'database',
                'locale' => 'vi',
                'subject' => null,
                'title' => 'Yêu cầu đặt lại mật khẩu',
                'content' => 'Mã đặt lại mật khẩu đã được gửi đến email của bạn.',
                'variables' => json_encode(['user_name', 'app_name', 'reset_code', 'expires_in']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $passwordResetType->id,
                'channel' => 'email',
                'locale' => 'vi',
                'subject' => 'Mã đặt lại mật khẩu - {app_name}',
                'title' => 'Yêu cầu đặt lại mật khẩu',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Đặt lại mật khẩu - {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">🔒 Đặt lại mật khẩu</h1>
    </div>

    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Xin chào <strong>{user_name}</strong>,</p>

        <p style="font-size: 16px; margin-bottom: 20px;">
            Bạn đã yêu cầu đặt lại mật khẩu cho tài khoản {app_name} của mình.
        </p>

        <p style="font-size: 16px; margin-bottom: 20px;">
            Mã đặt lại mật khẩu của bạn là:
        </p>

        <div style="text-align: center; margin: 30px 0;">
            <div style="background: #dc3545; color: white; padding: 20px; border-radius: 10px; font-size: 32px; font-weight: bold; letter-spacing: 8px; display: inline-block; min-width: 200px;">
                {reset_code}
            </div>
        </div>

        <p style="font-size: 16px; margin-bottom: 20px; text-align: center;">
            <strong>Mã này sẽ hết hạn sau {expires_in} phút.</strong>
        </p>

        <p style="font-size: 14px; color: #666; margin-top: 20px;">
            Nếu bạn không yêu cầu đặt lại mật khẩu này, vui lòng bỏ qua email này và mật khẩu của bạn sẽ không thay đổi.
        </p>

        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">

        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Trân trọng,<br>
            Đội ngũ Bảo mật {app_name}
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'app_name', 'reset_code', 'expires_in']),
                'settings' => json_encode(['from_name' => 'Đội ngũ Bảo mật {app_name}', 'priority' => 'high']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Password Changed Templates
        if (isset($notificationTypes['password_changed'])) {
            $passwordChangedType = $notificationTypes['password_changed'];

            // English templates
            $templates[] = [
                'notification_type_id' => $passwordChangedType->id,
                'channel' => 'database',
                'locale' => 'en',
                'subject' => null,
                'title' => 'Password Changed Successfully',
                'content' => 'Your password has been changed successfully.',
                'variables' => json_encode(['user_name', 'app_name', 'change_time']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $passwordChangedType->id,
                'channel' => 'email',
                'locale' => 'en',
                'subject' => 'Password Changed - {app_name}',
                'title' => 'Password Changed Successfully',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Changed - {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">✅ Password Changed</h1>
    </div>

    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Hi <strong>{user_name}</strong>,</p>

        <p style="font-size: 16px; margin-bottom: 20px;">
            Your password for {app_name} has been changed successfully at {change_time}.
        </p>

        <p style="font-size: 16px; margin-bottom: 20px;">
            If you didn\'t make this change, please contact our support team immediately.
        </p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{support_url}" style="background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                Contact Support
            </a>
        </div>

        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">

        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Best regards,<br>
            The {app_name} Security Team
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'app_name', 'change_time', 'support_url']),
                'settings' => json_encode(['from_name' => '{app_name} Security', 'priority' => 'normal']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // Vietnamese templates
            $templates[] = [
                'notification_type_id' => $passwordChangedType->id,
                'channel' => 'database',
                'locale' => 'vi',
                'subject' => null,
                'title' => 'Đổi mật khẩu thành công',
                'content' => 'Mật khẩu của bạn đã được thay đổi thành công.',
                'variables' => json_encode(['user_name', 'app_name', 'change_time']),
                'settings' => json_encode([]),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $templates[] = [
                'notification_type_id' => $passwordChangedType->id,
                'channel' => 'email',
                'locale' => 'vi',
                'subject' => 'Mật khẩu đã được thay đổi - {app_name}',
                'title' => 'Đổi mật khẩu thành công',
                'content' => '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mật khẩu đã được thay đổi - {app_name}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">✅ Mật khẩu đã được thay đổi</h1>
    </div>

    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">Xin chào <strong>{user_name}</strong>,</p>

        <p style="font-size: 16px; margin-bottom: 20px;">
            Mật khẩu của bạn cho tài khoản {app_name} đã được thay đổi thành công lúc {change_time}.
        </p>

        <p style="font-size: 16px; margin-bottom: 20px;">
            Nếu bạn không thực hiện thay đổi này, vui lòng liên hệ với đội ngũ hỗ trợ của chúng tôi ngay lập tức.
        </p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{support_url}" style="background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                Liên hệ hỗ trợ
            </a>
        </div>

        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">

        <p style="font-size: 14px; color: #666; text-align: center; margin: 0;">
            Trân trọng,<br>
            Đội ngũ Bảo mật {app_name}
        </p>
    </div>
</body>
</html>',
                'variables' => json_encode(['user_name', 'app_name', 'change_time', 'support_url']),
                'settings' => json_encode(['from_name' => 'Đội ngũ Bảo mật {app_name}', 'priority' => 'normal']),
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Insert templates in batches
        if (!empty($templates)) {
            foreach (array_chunk($templates, 50) as $chunk) {
                foreach ($chunk as $template) {
                    DB::table('notification_templates')->updateOrInsert(
                        [
                            'notification_type_id' => $template['notification_type_id'],
                            'channel' => $template['channel'],
                            'locale' => $template['locale']
                        ],
                        $template
                    );
                }
            }
        }

        $this->command->info('Auth notification templates seeded successfully. Total: ' . count($templates));
    }
}

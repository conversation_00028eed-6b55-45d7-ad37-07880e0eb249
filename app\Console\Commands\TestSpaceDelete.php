<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\Organization\Models\Organization;
use Modules\User\Models\User;
use Modules\Organization\Http\Requests\Organization\DestroyOrganizationRequest;
use Illuminate\Http\Request;
use Illuminate\Routing\Route;

class TestSpaceDelete extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:space-delete {user_id} {organization_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test space deletion (organization via space route)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id');
        $organizationId = $this->argument('organization_id');
        
        $user = User::find($userId);
        $organization = Organization::find($organizationId);
        
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return;
        }
        
        if (!$organization) {
            $this->error("Organization with ID {$organizationId} not found");
            return;
        }

        $this->info("Testing space deletion (organization via space route):");
        $this->info("User: {$user->email} (ID: {$user->id})");
        $this->info("Organization: {$organization->name} (ID: {$organization->id})");
        $this->info("Organization Owner ID: {$organization->owner_id}");
        
        try {
            // Test policy directly
            $canDelete = $user->can('delete', $organization);
            $this->info("Policy allows delete: " . ($canDelete ? 'YES' : 'NO'));
            
            // Test request authorization with space route
            $request = new DestroyOrganizationRequest();
            $request->setUserResolver(function() use ($user) {
                return $user;
            });
            
            // Mock route for space (not organization)
            $route = new Route(['DELETE'], '/api/v1/auth/spaces/{space}', []);
            $route->setParameter('space', $organization); // This is the key difference
            $route->bind(new Request());
            
            $request->setRouteResolver(function() use ($route) {
                return $route;
            });
            
            $canAuthorize = $request->authorize();
            $this->info("Request authorize (space route): " . ($canAuthorize ? 'YES' : 'NO'));
            
            if ($canAuthorize) {
                $this->info("✓ User can delete this organization via space route");
            } else {
                $this->error("✗ User cannot delete this organization via space route");
                
                // Check specific reasons
                if ($user->id !== $organization->owner_id) {
                    $this->error("Reason: User is not the owner of this organization");
                } else {
                    $this->error("Reason: Unknown - check logs for more details");
                }
            }
            
        } catch (\Exception $e) {
            $this->error("✗ Failed to test space deletion:");
            $this->error($e->getMessage());
            $this->error("File: {$e->getFile()}:{$e->getLine()}");
            
            if ($this->option('verbose')) {
                $this->error("Stack trace:");
                $this->error($e->getTraceAsString());
            }
        }
    }
}

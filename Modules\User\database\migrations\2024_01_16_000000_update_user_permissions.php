<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Role\Models\Permission;
use Modules\Role\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Permission mapping from old to new
        $permissionMapping = [
            'user.read' => 'user.view',
            'user.update' => 'user.edit',
            'user.destroy' => 'user.delete',
            'user.force-delete' => 'user.destroy',
        ];

        foreach ($permissionMapping as $oldName => $newName) {
            // Update permission name
            Permission::where('name', $oldName)
                ->where('guard_name', 'api')
                ->update(['name' => $newName]);
        }

        // Remove user.restore permission as it's now part of user.delete
        Permission::where('name', 'user.restore')
            ->where('guard_name', 'api')
            ->delete();

        // Update display names and descriptions
        $permissionUpdates = [
            'user.view' => [
                'display_name' => 'View Users',
                'description' => 'Permission to view users list and details',
            ],
            'user.edit' => [
                'display_name' => 'Edit Users',
                'description' => 'Permission to update existing users and assign roles',
            ],
            'user.delete' => [
                'display_name' => 'Delete Users',
                'description' => 'Permission to soft delete and restore users',
            ],
            'user.destroy' => [
                'display_name' => 'Destroy Users',
                'description' => 'Permission to permanently delete users',
            ],
        ];

        foreach ($permissionUpdates as $name => $updates) {
            Permission::where('name', $name)
                ->where('guard_name', 'api')
                ->update($updates);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse permission mapping
        $reverseMapping = [
            'user.view' => 'user.read',
            'user.edit' => 'user.update',
            'user.delete' => 'user.destroy',
            'user.destroy' => 'user.force-delete',
        ];

        foreach ($reverseMapping as $newName => $oldName) {
            Permission::where('name', $newName)
                ->where('guard_name', 'api')
                ->update(['name' => $oldName]);
        }

        // Recreate user.restore permission
        Permission::create([
            'name' => 'user.restore',
            'display_name' => 'Restore Users',
            'description' => 'Permission to restore soft deleted users',
            'module_name' => 'user',
            'sort_order' => 5,
            'guard_name' => 'api',
        ]);

        // Restore old display names and descriptions
        $oldUpdates = [
            'user.read' => [
                'display_name' => 'View Users',
                'description' => 'Permission to view users list and details',
            ],
            'user.update' => [
                'display_name' => 'Update Users',
                'description' => 'Permission to update existing users and assign roles',
            ],
            'user.destroy' => [
                'display_name' => 'Delete Users',
                'description' => 'Permission to soft delete users and bulk delete',
            ],
            'user.force-delete' => [
                'display_name' => 'Force Delete Users',
                'description' => 'Permission to permanently delete users',
            ],
        ];

        foreach ($oldUpdates as $name => $updates) {
            Permission::where('name', $name)
                ->where('guard_name', 'api')
                ->update($updates);
        }
    }
};

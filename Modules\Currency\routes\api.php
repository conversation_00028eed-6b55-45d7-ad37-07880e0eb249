<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\Currency\Http\Controllers\CurrencyController;
use Mo<PERSON>les\Currency\Http\Controllers\Auth\CurrencyController as AuthCurrencyController;
use Mo<PERSON>les\Currency\Http\Controllers\Auth\ExchangeRateController;

// Public API routes (no authentication required)
Route::prefix('v1')->group(function () {
    Route::get('currencies', [CurrencyController::class, 'index'])->name('api.currency.index');
    Route::get('currencies/default', [CurrencyController::class, 'default'])->name('api.currency.default');
    Route::get('currencies/codes', [CurrencyController::class, 'codes'])->name('api.currency.codes');
    Route::get('currencies/{code}', [CurrencyController::class, 'show'])->name('api.currency.show');

    // Currency conversion endpoints
    Route::post('currencies/convert', [CurrencyController::class, 'convert'])->name('api.currency.convert');
    Route::post('currencies/exchange-rate', [CurrencyController::class, 'exchangeRate'])->name('api.currency.exchange-rate');
    Route::post('currencies/format', [CurrencyController::class, 'format'])->name('api.currency.format');
});

// Authenticated API routes (admin/auth)
Route::middleware(['auth:api'])->prefix('v1/auth')->group(function () {
    // Currency dropdown
    Route::get('currencies/dropdown', [AuthCurrencyController::class, 'dropdown'])->name('auth.currency.dropdown');

    // Standard CRUD routes
    Route::get('currencies', [AuthCurrencyController::class, 'index'])->name('auth.currency.index');
    Route::post('currencies', [AuthCurrencyController::class, 'store'])->name('auth.currency.store');
    Route::get('currencies/{id}', [AuthCurrencyController::class, 'show'])->name('auth.currency.show');
    Route::put('currencies/{id}', [AuthCurrencyController::class, 'update'])->name('auth.currency.update');
    Route::patch('currencies/{id}', [AuthCurrencyController::class, 'update'])->name('auth.currency.update');

    // Delete operations
    Route::delete('currencies/{id}/delete', [AuthCurrencyController::class, 'delete'])->name('auth.currency.delete');
    Route::delete('currencies/{id}/destroy', [AuthCurrencyController::class, 'destroy'])->name('auth.currency.destroy');
    Route::put('currencies/{id}/restore', [AuthCurrencyController::class, 'restore'])->name('auth.currency.restore');

    // Bulk operations
    Route::delete('currencies/bulk/delete', [AuthCurrencyController::class, 'bulkDelete'])->name('auth.currency.bulk-delete');
    Route::delete('currencies/bulk/destroy', [AuthCurrencyController::class, 'bulkDestroy'])->name('auth.currency.bulk-destroy');
    Route::put('currencies/bulk/restore', [AuthCurrencyController::class, 'bulkRestore'])->name('auth.currency.bulk-restore');

    // Exchange Rate routes
    Route::get('exchange-rates/stale', [ExchangeRateController::class, 'stale'])->name('auth.exchange-rate.stale');
    Route::post('exchange-rates/bulk-update', [ExchangeRateController::class, 'bulkUpdate'])->name('auth.exchange-rate.bulk-update');
    Route::post('exchange-rates/refresh', [ExchangeRateController::class, 'refresh'])->name('auth.exchange-rate.refresh');
    Route::apiResource('exchange-rates', ExchangeRateController::class)->names('auth.exchange-rate');
});

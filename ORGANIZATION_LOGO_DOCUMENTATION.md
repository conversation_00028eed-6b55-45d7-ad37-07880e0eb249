# Organization Logo Functionality Documentation

## Overview
This document describes the Organization logo functionality that has been implemented, including upload, storage, and management features.

## Features Implemented

### 1. Logo Upload API
- **Endpoint**: `POST /api/v1/auth/organizations/upload-logo`
- **Purpose**: Upload logo files to temporary storage
- **Authentication**: Required (any authenticated user can upload)
- **File Requirements**:
  - Required field: `logo`
  - Supported formats: jpeg, jpg, png, gif, svg, webp
  - Maximum size: 10MB
  - Must be a valid image file

### 2. Organization Creation with Logo
- When creating an organization, you can include a `logo` field with the temporary path returned from the upload API
- The system automatically moves the logo from temp directory to the organization's permanent directory
- Temp files are cleaned up after successful organization creation

### 3. Logo URL Accessor
- Organizations have a `logo_url` attribute that automatically generates the correct URL
- **Default behavior**: Returns `/storage/organization-avatars/organization.png` when no logo is set
- **Storage paths**: Converts relative storage paths to full URLs using `Storage::url()`
- **External URLs**: Returns external URLs as-is

### 4. File Organization
- **Temp directory**: `storage/app/public/organization-logos/temp/`
- **Organization directory**: `storage/app/public/organization-logos/{organization-uuid}/`
- **Default logo**: `storage/app/public/organization-avatars/organization.png`

## API Usage Examples

### Step 1: Upload Logo to Temporary Storage
```bash
curl -X POST \
  http://localhost/api/v1/auth/organizations/upload-logo \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Accept: application/json' \
  -F 'logo=@/path/to/your/logo.png'
```

**Response:**
```json
{
  "success": true,
  "message": "Logo uploaded successfully to temporary storage.",
  "data": {
    "logo_path": "organization-logos/temp/1751821190_7333cb5c-1e06-4ce6-a322-462ec7f905d1.png",
    "logo_url": "/storage/organization-logos/temp/1751821190_7333cb5c-1e06-4ce6-a322-462ec7f905d1.png"
  }
}
```

### Step 2: Create Organization with Logo
```bash
curl -X POST \
  http://localhost/api/v1/auth/organizations \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "My Organization",
    "email": "<EMAIL>",
    "logo": "organization-logos/temp/1751821190_7333cb5c-1e06-4ce6-a322-462ec7f905d1.png"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Organization created successfully.",
  "data": {
    "uuid": "242235c3-ace7-4f8b-a613-966045c5e9fd",
    "name": "My Organization",
    "email": "<EMAIL>",
    "logo": "organization-logos/242235c3-ace7-4f8b-a613-966045c5e9fd/1751821190_7333cb5c-1e06-4ce6-a322-462ec7f905d1.png",
    "logo_url": "/storage/organization-logos/242235c3-ace7-4f8b-a613-966045c5e9fd/1751821190_7333cb5c-1e06-4ce6-a322-462ec7f905d1.png",
    ...
  }
}
```

## File Structure
```
storage/app/public/
├── organization-logos/
│   ├── temp/                          # Temporary uploads
│   │   └── {timestamp}_{uuid}.{ext}   # Temp files
│   └── {organization-uuid}/           # Organization-specific directories
│       └── {timestamp}_{uuid}.{ext}   # Permanent logo files
└── organization-avatars/
    └── organization.png               # Default organization logo
```

## Implementation Details

### Files Modified/Created

1. **Organization Model** (`Modules/Organization/app/Models/Organization.php`)
   - Added `logo_url` to `$appends` array
   - Implemented `getLogoUrlAttribute()` method

2. **Organization Controller** (`Modules/Organization/app/Http/Controllers/Auth/OrganizationController.php`)
   - Added `uploadLogo()` method for temporary uploads
   - Added `handleLogoUpload()` private method
   - Added `moveLogoFromTemp()` public method
   - Modified `store()` method to handle logo from temp

3. **Logo Upload Request** (`Modules/Organization/app/Http/Requests/Organization/OrganizationLogoUploadRequest.php`)
   - Created validation rules for logo uploads
   - Authorization allows any authenticated user

4. **API Routes** (`Modules/Organization/routes/api.php`)
   - Added `POST organizations/upload-logo` route

5. **Default Logo File** (`storage/app/public/organization-avatars/organization.png`)
   - Created default SVG logo for organizations

6. **Tests** (`Modules/Organization/tests/Feature/OrganizationLogoTest.php`)
   - Comprehensive test suite for logo functionality

## Security Considerations

- File type validation ensures only image files are uploaded
- File size limit prevents abuse (10MB max)
- Unique filenames prevent conflicts
- Organization-specific directories prevent unauthorized access
- Temporary files are cleaned up automatically

## Workflow Summary

1. **Upload**: User uploads logo → stored in temp directory → returns temp path
2. **Create**: User creates organization with temp path → logo moved to organization directory → temp file deleted
3. **Access**: Organization logo_url automatically generates correct URL based on storage location
4. **Default**: Organizations without logos show default organization.png

## Automatic Cleanup

### Temporary File Cleanup
The system includes automatic cleanup of temporary logo files to prevent storage bloat:

- **Command**: `php artisan organization:cleanup-temp-logos`
- **Schedule**: Runs daily at 2:00 AM automatically
- **Default retention**: 24 hours (configurable with `--hours` option)
- **Purpose**: Removes abandoned temp files from failed uploads or incomplete organization creation

### Manual Cleanup
You can manually run cleanup with custom retention periods:

```bash
# Clean files older than 24 hours (default)
php artisan organization:cleanup-temp-logos

# Clean files older than 12 hours
php artisan organization:cleanup-temp-logos --hours=12

# Clean files older than 1 hour
php artisan organization:cleanup-temp-logos --hours=1
```

### Failed Login Attempts
**Important**: The system does **NOT** automatically clean up temporary files when login attempts fail. Temporary files are only cleaned up by:

1. **Successful organization creation** (files moved to permanent location)
2. **Scheduled cleanup task** (runs daily at 2:00 AM)
3. **Manual cleanup command** (run by administrators)

This means if a user uploads a logo but never completes organization creation (due to failed login, abandonment, etc.), the temporary file will remain until the next scheduled cleanup.

## Security Considerations

- File type validation ensures only image files are uploaded
- File size limit prevents abuse (10MB max)
- Unique filenames prevent conflicts
- Organization-specific directories prevent unauthorized access
- Temporary files are cleaned up automatically to prevent storage bloat
- **Temporary files persist through failed login attempts** until scheduled cleanup

This implementation provides a secure, organized, and user-friendly logo management system for organizations.

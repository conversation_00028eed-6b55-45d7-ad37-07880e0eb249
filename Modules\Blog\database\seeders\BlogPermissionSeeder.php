<?php

namespace Modules\Blog\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Role\Models\Permission;
use Modules\Role\Models\Role;

class BlogPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedBlogPermissions();
        $this->assignPermissionsToSuperAdmin();
    }

    /**
     * Seed blog module permissions.
     */
    private function seedBlogPermissions(): void
    {
        $permissions = [
            // Blog Post Permissions
            [
                'name' => 'blog.post.view',
                'display_name' => 'View Blog Posts',
                'description' => 'Permission to view blog posts list and details',
                'module_name' => 'blog',
                'sort_order' => 1,
                'guard_name' => 'api',
            ],
            [
                'name' => 'blog.post.create',
                'display_name' => 'Create Blog Posts',
                'description' => 'Permission to create new blog posts',
                'module_name' => 'blog',
                'sort_order' => 2,
                'guard_name' => 'api',
            ],
            [
                'name' => 'blog.post.edit',
                'display_name' => 'Edit Blog Posts',
                'description' => 'Permission to update existing blog posts',
                'module_name' => 'blog',
                'sort_order' => 3,
                'guard_name' => 'api',
            ],
            [
                'name' => 'blog.post.delete',
                'display_name' => 'Delete Blog Posts',
                'description' => 'Permission to soft delete and restore blog posts',
                'module_name' => 'blog',
                'sort_order' => 4,
                'guard_name' => 'api',
            ],
            [
                'name' => 'blog.post.destroy',
                'display_name' => 'Destroy Blog Posts',
                'description' => 'Permission to permanently delete blog posts',
                'module_name' => 'blog',
                'sort_order' => 5,
                'guard_name' => 'api',
            ],
            
            // Blog Category Permissions
            [
                'name' => 'blog.category.view',
                'display_name' => 'View Blog Categories',
                'description' => 'Permission to view blog categories list and details',
                'module_name' => 'blog',
                'sort_order' => 6,
                'guard_name' => 'api',
            ],
            [
                'name' => 'blog.category.create',
                'display_name' => 'Create Blog Categories',
                'description' => 'Permission to create new blog categories',
                'module_name' => 'blog',
                'sort_order' => 7,
                'guard_name' => 'api',
            ],
            [
                'name' => 'blog.category.edit',
                'display_name' => 'Edit Blog Categories',
                'description' => 'Permission to update existing blog categories',
                'module_name' => 'blog',
                'sort_order' => 8,
                'guard_name' => 'api',
            ],
            [
                'name' => 'blog.category.delete',
                'display_name' => 'Delete Blog Categories',
                'description' => 'Permission to soft delete and restore blog categories',
                'module_name' => 'blog',
                'sort_order' => 9,
                'guard_name' => 'api',
            ],
            [
                'name' => 'blog.category.destroy',
                'display_name' => 'Destroy Blog Categories',
                'description' => 'Permission to permanently delete blog categories',
                'module_name' => 'blog',
                'sort_order' => 10,
                'guard_name' => 'api',
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => 'api'],
                array_merge($permission, ['guard_name' => 'api'])
            );
        }
    }

    /**
     * Assign blog permissions to super-admin role.
     */
    private function assignPermissionsToSuperAdmin(): void
    {
        $superAdmin = Role::where('name', 'super-admin')->where('guard_name', 'api')->first();

        if ($superAdmin) {
            $blogPermissions = Permission::where('module_name', 'blog')->where('guard_name', 'api')->get();
            
            // Gán tất cả quyền blog cho super-admin
            foreach ($blogPermissions as $permission) {
                if (!$superAdmin->hasPermissionTo($permission->name)) {
                    $superAdmin->givePermissionTo($permission->name);
                }
            }
        }
    }
}

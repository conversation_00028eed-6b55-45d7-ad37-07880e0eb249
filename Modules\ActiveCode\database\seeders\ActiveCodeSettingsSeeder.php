<?php

namespace Modules\ActiveCode\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Setting\Models\Setting;
use Modules\Setting\Models\SettingGroup;

class ActiveCodeSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        // Create or get ActiveCode settings group
        $settingGroup = SettingGroup::updateOrCreate(
            ['key' => 'activecode'],
            [
                'label' => 'Active Code Settings',
                'icon' => 'fas fa-shield-alt',
                'sort_order' => 10,
            ]
        );

        // Define ActiveCode settings
        $settings = [
            [
                'key' => 'activecode_expiry_minutes',
                'value' => '15',
                'type' => 'integer',
                'input_type' => 'number',
                'label' => 'Code Expiry Time (Minutes)',
                'description' => 'How long verification codes remain valid before expiring',
                'validation_rules' => 'required|integer|min:1|max:1440',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'activecode_max_attempts',
                'value' => '5',
                'type' => 'integer',
                'input_type' => 'number',
                'label' => 'Maximum Verification Attempts',
                'description' => 'Maximum number of attempts allowed before code is blocked',
                'validation_rules' => 'required|integer|min:1|max:20',
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'activecode_rate_limit_seconds',
                'value' => '60',
                'type' => 'integer',
                'input_type' => 'number',
                'label' => 'Rate Limit Time (Seconds)',
                'description' => 'Minimum time between resend requests',
                'validation_rules' => 'required|integer|min:10|max:300',
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'activecode_length',
                'value' => '6',
                'type' => 'integer',
                'input_type' => 'select',
                'label' => 'Code Length',
                'description' => 'Number of digits in verification codes',
                'validation_rules' => 'required|integer|in:4,5,6,7,8',
                'options' => [
                    '4' => '4 digits',
                    '5' => '5 digits',
                    '6' => '6 digits',
                    '7' => '7 digits',
                    '8' => '8 digits',
                ],
                'is_public' => false,
                'sort_order' => 4,
            ],
            [
                'key' => 'activecode_cleanup_days',
                'value' => '1',
                'type' => 'integer',
                'input_type' => 'number',
                'label' => 'Cleanup Retention (Days)',
                'description' => 'How long to keep expired codes before cleanup',
                'validation_rules' => 'required|integer|min:1|max:30',
                'is_public' => false,
                'sort_order' => 5,
            ],
        ];

        // Create or update each setting
        foreach ($settings as $settingData) {
            $settingData['group_id'] = $settingGroup->id;
            
            Setting::updateOrCreate(
                ['key' => $settingData['key']],
                $settingData
            );
        }

        $this->command->info('ActiveCode settings seeded successfully.');
    }
}

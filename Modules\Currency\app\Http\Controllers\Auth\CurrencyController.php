<?php

namespace Modules\Currency\Http\Controllers\Auth;

use Modules\Core\Traits\ResponseTrait;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Mo<PERSON>les\Currency\Http\Filters\CurrencyFilter;
use Modules\Currency\Http\Requests\BulkCurrencyRequest;
use Modules\Currency\Http\Requests\BulkCurrencyDestroyRequest;
use Modules\Currency\Http\Requests\CurrencyRequest;
use Modules\Currency\Models\Currency;

class CurrencyController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {
        $this->middleware('role_or_permission:super-admin|currency.view')->only(['index', 'show', 'dropdown']);
        $this->middleware('role_or_permission:super-admin|currency.create')->only(['store']);
        $this->middleware('role_or_permission:super-admin|currency.edit')->only(['update']);
        $this->middleware('role_or_permission:super-admin|currency.delete')->only(['delete', 'restore', 'bulkDelete', 'bulkRestore']);
        $this->middleware('role_or_permission:super-admin|currency.destroy')->only(['destroy', 'bulkDestroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $currencies = Currency::query()
            ->filter(new CurrencyFilter($request))
            ->paginate($request->input('limit', 10));

        return $this->paginatedResponse($currencies, __('Currencies retrieved successfully.'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CurrencyRequest $request): JsonResponse
    {
        $currency = Currency::create($request->all());

        return $this->successResponse($currency, __('Currency created successfully.'), 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Currency $currency): JsonResponse
    {
        return $this->successResponse($currency, __('Currency retrieved successfully.'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CurrencyRequest $request, Currency $currency): JsonResponse
    {
        $currency->update($request->all());

        return $this->successResponse($currency->fresh(), __('Currency updated successfully.'));
    }

    /**
     * Soft delete the specified resource from storage.
     */
    public function delete(int $id): JsonResponse
    {
        $currency = Currency::findOrFail($id);

        // Check if currency has exchange rates
        $hasExchangeRates = $currency->baseExchangeRates()->exists() || $currency->targetExchangeRates()->exists();

        if ($hasExchangeRates) {
            return $this->errorResponse($currency->id, __('Cannot delete currency that has exchange rates.'), 422);
        }

        $currency->delete();

        return $this->successResponse($currency->id, __('Currency deleted successfully.'));
    }

    /**
     * Restore the specified resource from trash.
     */
    public function restore(int $id): JsonResponse
    {
        $currency = Currency::onlyTrashed()->findOrFail($id);
        $currency->restore();

        return $this->successResponse($currency, __('Currency restored successfully.'));
    }

    /**
     * Permanently delete the specified resource.
     */
    public function destroy(int $id): JsonResponse
    {
        $currency = Currency::onlyTrashed()->findOrFail($id);
        $currency->forceDelete();

        return $this->successResponse([$id], __('Currency permanently deleted.'));
    }

    /**
     * Bulk soft delete multiple resources.
     */
    public function bulkDelete(BulkCurrencyRequest $request): JsonResponse
    {
        $ids = $request->validated('ids');

        $deletedCount = Currency::query()->whereIn('id', $ids)->delete();

        return $this->successResponse(
            ['deleted_count' => $deletedCount],
            __(':count currencies deleted successfully.', ['count' => $deletedCount])
        );
    }

    /**
     * Bulk restore multiple resources.
     */
    public function bulkRestore(BulkCurrencyRequest $request): JsonResponse
    {
        $ids = $request->validated('ids');

        $restoredCount = Currency::onlyTrashed()->whereIn('id', $ids)->restore();

        return $this->successResponse(
            ['restored_count' => $restoredCount],
            __(':count currencies restored successfully.', ['count' => $restoredCount])
        );
    }

    /**
     * Bulk permanently delete multiple resources.
     */
    public function bulkDestroy(BulkCurrencyDestroyRequest $request): JsonResponse
    {
        $ids = $request->validated('ids');

        $deletedCount = Currency::onlyTrashed()->whereIn('id', $ids)->forceDelete();

        return $this->successResponse(
            ['deleted_count' => $deletedCount],
            __(':count currencies permanently deleted.', ['count' => $deletedCount])
        );
    }

    /**
     * Get currencies for dropdown.
     */
    public function dropdown(): JsonResponse
    {
        $currencies = Currency::query()->active()->get();
        return $this->successResponse(
            $currencies->makeHidden(['id', 'updated_at', 'created_at', 'status']),
            __('Currencies retrieved successfully.')
        );
    }
}

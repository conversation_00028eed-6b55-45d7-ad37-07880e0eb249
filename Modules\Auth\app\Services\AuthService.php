<?php

namespace Modules\Auth\Services;

use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Redirect;
use Laravel\Socialite\Facades\Socialite;
use Modules\ActiveCode\Models\ActiveCode;
use Modules\Auth\Http\Requests\UpdateProfileRequest;
use Modules\Auth\Models\UserSocialAccount;
use Modules\User\Models\User;
use Modules\User\Enums\UserStatus;
use Modules\User\Enums\UserGender;
use Modules\ActiveCode\Facades\ActiveCodeFacade;
use Ty<PERSON>\JWTAuth\Facades\JWTAuth;

class AuthService
{
    /**
     * Register a new user and send verification OTP.
     */
    public function register(array $data): array
    {
        $verificationRequired = setting_bool('auth.account.verification.required');

        $user = User::create([
            'username' => $data['username'],
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'gender' => $data['gender'] ?? 'other',
            'phone' => $data['phone'] ?? null,
            'status' => $verificationRequired ? UserStatus::Pending : UserStatus::Active,
            'is_verified' => false,
            'email_verified_at' => null,
        ]);

        $user->active_code = ActiveCodeFacade::generate(
            $user->email,
            'account_verification',
            30
        );

        return $user->toArray();
    }

    /**
     * Verify email with OTP code.
     */
    public function verifyEmail(string $email, string $code): array|string
    {
        $user = User::where('email', $email)->first();
        if (!$user) {
            return __('User not found.');
        }
        // Verify OTP code
        $resultVerify = ActiveCodeFacade::verify($code, $email, 'account_verification');

        if (!$resultVerify) {
            return __('Invalid verification code.');
        }

        // Update user verification status
        $updateData = [
            'email_verified_at' => now(),
            'is_verified' => true,
        ];

        if ($user->status === UserStatus::Pending) {
            $updateData['status'] = UserStatus::Active;
        }

        $user->update($updateData);

        return $user->toArray();
    }

    /**
     * Resend verification code.
     */
    public function resendVerificationCode(string $email): array|string
    {
        $user = User::where('email', $email)->first();

        if (!$user) {
            return __('User not found.');
        }

        // Check rate limiting
        $activeCode = ActiveCodeFacade::resend($email, 'account_verification');

        if (!$activeCode) {
            return __('Invalid verification code.');
        }

        // Update user's active code
        $user->active_code = $activeCode->code;
        return $user->toArray();
    }

    /**
     * Login user.
     */
    public function login(array $credentials): array|string
    {
        // Find user by email or username
        $user = User::where('email', $credentials['login'])
            ->orWhere('username', $credentials['login'])
            ->first();

        if (!$user || !Hash::check($credentials['password'], $user->password)) {
            $rateLimitService = app(AuthRateLimitService::class);
            $rateLimitService->recordAttempt(request(), 'login', false, 'invalid_credentials');
            return __('User not found or password mismatch');
        }

        if (!$user->canLogin()) {
            $rateLimitService = app(AuthRateLimitService::class);
            $rateLimitService->recordAttempt(request(), 'login', false, 'account_inactive');

            return __('Account is inactive or suspended');
        }

        // Generate JWT token
        $token = JWTAuth::fromUser($user);

        // Update last login info
        $user->update([
            'last_login_at' => now(),
            'last_login_ip' => request()->ip(),
        ]);

        // Record successful login attempt
        $rateLimitService = app(AuthRateLimitService::class);
        $rateLimitService->recordAttempt(request(), 'login', true);

        // Load user with relationships first
        $user->load(['geoDivision:id,name,native_name,country_id', 'country:id,name,native_name']);

        // Get permissions and roles for API guard and add to user data
        $permissions = $user->getAllPermissions('api');
        $roles = $user->getRoleNames('api');

        $userData = $user->toArray();
        $userData['permissions'] = $permissions->pluck('name')->toArray();
        $userData['roles'] = $roles->toArray();

        return [
            'success' => true,
            'data' => [
                'user' => $userData,
                'token' => $token,
                'token_type' => 'bearer',
                'expires_in' => cacheTTL() * 60,
            ]
        ];
    }

    /**
     * Logout user.
     */
    public function logout(): void
    {
        JWTAuth::invalidate(JWTAuth::getToken());
    }

    /**
     * Refresh JWT token.
     */
    public function refresh(): array
    {
        $token = JWTAuth::refresh(JWTAuth::getToken());

        return [
            'token' => $token,
            'token_type' => 'bearer',
            'expires_in' => cacheTTL() * 60,
        ];
    }

    /**
     * Update user profile.
     */
    public function updateProfile($request): User
    {
        $user = Auth::user();

        $user->update($request->only([
            'first_name',
            'last_name',
            'avatar',
            'birthday',
            'gender',
            'email',
            'phone',
            'phone_verified_at',
            'address',
            'geo_division_id',
            'country_id',
            'newsletter_subscribed',
        ]));

        return $user->load(['geoDivision:id,name,native_name,country_id', 'country:id,name,native_name']);
    }

    /**
     * Send password reset code.
     */
    public function sendPasswordResetCode(string $email): array
    {
        $user = User::where('email', $email)->first();

        if (!$user) {
            return [
                'success' => false,
                'message' => __('User not found.')
            ];
        }

        // Generate password reset code
        $activeCode = ActiveCodeFacade::generate(
            $email,
            'password_reset',
            30 // 30 minutes expiry
        );

        return [
            'success' => true,
            'message' => __('Password reset code sent to your email.'),
            'user' => $user->toArray(),
            'reset_code' => $activeCode->code,
            'expires_at' => $activeCode->expired_at,
        ];
    }

    /**
     * Reset password with OTP.
     */
    public function resetPassword(string $email, string $code, string $newPassword): string|bool
    {
        $user = User::where('email', $email)->first();

        if (!$user) {
            return __('User not found.');
        }

        // Verify OTP code
        $result = ActiveCodeFacade::verify($code, $email, 'password_reset');

        if (!$result) {
            return __('Invalid reset code.');
        }

        // Update password
        $user->update([
            'password' => Hash::make($newPassword),
        ]);

        // Invalidate all existing tokens for security (if user has any active tokens)
        try {
            $token = JWTAuth::fromUser($user);
            JWTAuth::invalidate($token);
        } catch (\Exception $e) {
            // User might not have any active tokens, which is fine
            logger()->info('No active tokens to invalidate for password reset', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
        }

        return true;
    }

    /**
     * Request password change OTP.
     */
    public function requestPasswordChange(string $currentPassword): string|ActiveCode
    {
        $user = Auth::user();

        if (!Hash::check($currentPassword, $user->password)) {
            return __('Current password is incorrect.');
        }

        // Generate password change code
        return ActiveCodeFacade::generate(
            $user->email,
            'password_change',
            15 // 15 minutes expiry
        );
    }

    /**
     * Confirm password change with OTP.
     */
    public function confirmPasswordChange(string $code, string $newPassword): string|bool
    {
        $user = Auth::user();

        // Verify OTP code
        $result = ActiveCodeFacade::verify($code, $user->email, 'password_change');

        if (!$result) {
            return __('Invalid password change code.');
        }

        // Update password
        $user->update([
            'password' => Hash::make($newPassword),
        ]);

        return true;
    }

    /**
     * Redirect to OAuth provider.
     */
    public function redirectToProvider(string $provider): RedirectResponse
    {
        // Configure Socialite with database settings
        $this->configureSocialiteProvider($provider);

        return Socialite::driver($provider)->redirect();
    }

    /**
     * Handle OAuth callback.
     */
    public function handleProviderCallback(string $provider): array
    {
        try {
            // Configure Socialite with database settings
            $this->configureSocialiteProvider($provider);

            $socialUser = Socialite::driver($provider)->user();

            // First, check if this social account already exists
            $socialAccount = UserSocialAccount::findByProvider($provider, $socialUser->getId());

            if ($socialAccount) {
                // Social account exists, login the associated user
                $user = $socialAccount->user;

                // Update social account info
                $socialAccount->update([
                    'provider_email' => $socialUser->getEmail(),
                    'provider_name' => $socialUser->getName(),
                    'provider_nickname' => $socialUser->getNickname(),
                    'provider_avatar' => $socialUser->getAvatar(),
                    'last_used_at' => now(),
                ]);

                // Update user login info
                $user->update([
                    'avatar' => $user->avatar ?: $socialUser->getAvatar(),
                    'last_login_at' => now(),
                    'last_login_ip' => request()->ip(),
                ]);
            } else {
                // Social account doesn't exist
                // Check if this is a linking request (user is trying to link provider to existing account)
                $linkUserId = session('oauth_link_user_id');

                if ($linkUserId) {
                    // This is a linking request
                    $user = User::find($linkUserId);
                    session()->forget('oauth_link_user_id');

                    if (!$user) {
                        throw new \Exception('User not found for linking.');
                    }

                    // Check if provider is already linked to another account
                    $existingAccount = UserSocialAccount::findByProvider($provider, $socialUser->getId());
                    if ($existingAccount) {
                        throw new \Exception('This ' . ucfirst($provider) . ' account is already linked to another user.');
                    }

                    // Link provider to existing user
                    $this->createSocialAccount($user, $provider, $socialUser);
                } else {
                    // Regular OAuth login - check if user exists by email
                    $user = User::where('email', $socialUser->getEmail())->first();

                    if ($user) {
                        // User exists, link this social account to existing user
                        $this->createSocialAccount($user, $provider, $socialUser);

                        // Update user info
                        $user->update([
                            'avatar' => $user->avatar ?: $socialUser->getAvatar(),
                            'last_login_at' => now(),
                            'last_login_ip' => request()->ip(),
                        ]);
                    } else {
                        // Create new user from OAuth data
                        $names = $this->parseFullName($socialUser->getName());

                        $user = User::create([
                            'username' => $this->generateUniqueUsername($socialUser->getEmail()),
                            'email' => $socialUser->getEmail(),
                            'first_name' => $names['first_name'],
                            'last_name' => $names['last_name'],
                            'avatar' => $socialUser->getAvatar(),
                            'gender' => UserGender::Other,
                            'status' => UserStatus::Active,
                            'is_verified' => true,
                            'email_verified_at' => now(),
                            'last_login_at' => now(),
                            'last_login_ip' => request()->ip(),
                        ]);

                        // Create social account for new user
                        $this->createSocialAccount($user, $provider, $socialUser);
                    }
                }
            }

            // Generate JWT token
            $token = JWTAuth::fromUser($user);

            return [
                'success' => true,
                'data' => [
                    'user' => $user->load(['geoDivision', 'country', 'roles', 'socialAccounts']),
                    'token' => $token,
                    'token_type' => 'bearer',
                    'expires_in' => config('jwt.ttl') * 60,
                ]
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => __('OAuth authentication failed: :error', ['error' => $e->getMessage()])
            ];
        }
    }

    /**
     * Configure Socialite provider with database settings.
     */
    private function configureSocialiteProvider(string $provider): void
    {
        $settings = settingGroup('oauth') ?? [];

        if (!$settings || !setting_bool("oauth.{$provider}.enabled")) {
            throw new \Exception("OAuth settings not found. Please configure OAuth settings in the database.");
        }

        // Lấy tất cả config của provider
        $providerSettings = $settings->filter(function ($value, $key) use ($provider) {
            return str_starts_with($key, "{$provider}.") && !str_ends_with($key, '.enabled');
        });

        // Convert sang format Laravel config
        $config = [];
        foreach ($providerSettings as $key => $value) {
            $field = str_replace("{$provider}.", '', $key);
            $config["services.{$provider}.{$field}"] = $value;
        }

        config($config);
    }

    /**
     * Parse full name into first and last name.
     */
    private function parseFullName(string $fullName): array
    {
        $parts = explode(' ', trim($fullName), 2);

        return [
            'first_name' => $parts[0] ?? 'User',
            'last_name' => $parts[1] ?? 'OAuth',
        ];
    }

    /**
     * Generate unique username from email.
     */
    private function generateUniqueUsername(string $email): string
    {
        $baseUsername = explode('@', $email)[0];
        $baseUsername = preg_replace('/[^a-zA-Z0-9]/', '', $baseUsername);

        if (strlen($baseUsername) < 3) {
            $baseUsername = 'user' . $baseUsername;
        }

        $username = $baseUsername;
        $counter = 1;

        while (User::where('username', $username)->exists()) {
            $username = $baseUsername . $counter;
            $counter++;
        }

        return $username;
    }

    /**
     * Create social account for user.
     */
    private function createSocialAccount(User $user, string $provider, $socialUser): UserSocialAccount
    {
        return UserSocialAccount::create([
            'user_id' => $user->id,
            'provider' => $provider,
            'provider_id' => $socialUser->getId(),
            'provider_email' => $socialUser->getEmail(),
            'provider_name' => $socialUser->getName(),
            'provider_nickname' => $socialUser->getNickname(),
            'provider_avatar' => $socialUser->getAvatar(),
            'last_used_at' => now(),
        ]);
    }

    /**
     * Get enabled OAuth providers.
     */
    public function getEnabledProviders(): array
    {
        $oauthSettings = settingGroup('oauth') ?? collect();

        $enabledProviders = [];

        // Get all enabled providers
        foreach ($oauthSettings as $key => $value) {
            if (str_ends_with($key, '.enabled') && setting_bool("oauth.{$key}")) {
                $provider = str_replace('.enabled', '', $key);
                $enabledProviders[] = [
                    'name' => $provider,
                    'display_name' => ucfirst($provider),
                    'icon' => $this->getProviderIcon($provider),
                ];
            }
        }

        return $enabledProviders;
    }

    /**
     * Get provider icon class.
     */
    private function getProviderIcon(string $provider): string
    {
        return match ($provider) {
            'google' => 'fab fa-google',
            'facebook' => 'fab fa-facebook',
            'twitter' => 'fab fa-twitter',
            'apple' => 'fab fa-apple',
            'telegram' => 'fab fa-telegram',
            'zalo' => 'fas fa-z',
            default => 'fas fa-user',
        };
    }

    /**
     * Link OAuth provider to existing user.
     */
    public function linkProvider(string $provider, User $user = null): array
    {
        $user = $user ?: Auth::user();

        if (!$user) {
            return [
                'success' => false,
                'message' => __('User not authenticated.')
            ];
        }

        // Check if provider is already linked
        if ($user->hasLinkedProvider($provider)) {
            return [
                'success' => false,
                'message' => __('Provider :provider is already linked to your account.', ['provider' => ucfirst($provider)])
            ];
        }

        // Store user ID in session for linking after OAuth callback
        session(['oauth_link_user_id' => $user->id]);

        return [
            'success' => true,
            'redirect_url' => route('auth.oauth.redirect', ['provider' => $provider])
        ];
    }

    /**
     * Unlink OAuth provider from user.
     */
    public function unlinkProvider(string $provider, User $user = null): array
    {
        $user = $user ?: Auth::user();

        if (!$user) {
            return [
                'success' => false,
                'message' => __('User not authenticated.')
            ];
        }

        $socialAccount = $user->getSocialAccount($provider);

        if (!$socialAccount) {
            return [
                'success' => false,
                'message' => __('Provider :provider is not linked to your account.', ['provider' => ucfirst($provider)])
            ];
        }

        // Check if user has password or other social accounts
        $hasPassword = !empty($user->password);
        $otherSocialAccounts = $user->socialAccounts()->where('provider', '!=', $provider)->count();

        if (!$hasPassword && $otherSocialAccounts === 0) {
            return [
                'success' => false,
                'message' => __('Cannot unlink the only authentication method. Please set a password first.')
            ];
        }

        $socialAccount->delete();

        return [
            'success' => true,
            'message' => __('Provider :provider has been unlinked from your account.', ['provider' => ucfirst($provider)])
        ];
    }

    /**
     * Get user's linked OAuth accounts.
     */
    public function getLinkedAccounts(User $user = null): array
    {
        $user = $user ?: Auth::user();

        if (!$user) {
            return [];
        }

        return $user->socialAccounts()
                   ->select(['provider', 'provider_name', 'provider_email', 'provider_avatar', 'last_used_at'])
                   ->get()
                   ->map(function ($account) {
                       return [
                           'provider' => $account->provider,
                           'display_name' => $account->provider_display_name,
                           'name' => $account->provider_name,
                           'email' => $account->provider_email,
                           'avatar' => $account->provider_avatar,
                           'icon' => $account->provider_icon,
                           'last_used_at' => $account->last_used_at,
                       ];
                   })
                   ->toArray();
    }
}

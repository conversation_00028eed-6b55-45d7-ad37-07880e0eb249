<?php

namespace Modules\ChatBot\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;

class BulkKnowledgeBaseRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ids' => 'required|array|min:1',
            'ids.*' => 'required|integer|exists:knowledge_bases,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ids.required' => 'At least one knowledge base ID is required.',
            'ids.array' => 'IDs must be provided as an array.',
            'ids.min' => 'At least one knowledge base ID is required.',
            'ids.*.required' => 'Each ID is required.',
            'ids.*.integer' => 'Each ID must be an integer.',
            'ids.*.exists' => 'One or more knowledge bases do not exist.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'ids' => 'knowledge base IDs',
            'ids.*' => 'knowledge base ID',
        ];
    }
}

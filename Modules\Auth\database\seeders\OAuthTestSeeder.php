<?php

namespace Modules\Auth\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Auth\Models\UserSocialAccount;
use Modules\User\Models\User;

class OAuthTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating test OAuth social accounts...');

        // Find or create test users
        $user1 = User::where('email', '<EMAIL>')->first();
        if (!$user1) {
            $user1 = User::create([
                'username' => 'testuser1',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'first_name' => 'Test',
                'last_name' => 'User',
                'gender' => 'other',
                'status' => 'active',
                'is_verified' => true,
                'email_verified_at' => now(),
            ]);
        }

        $user2 = User::where('email', '<EMAIL>')->first();
        if (!$user2) {
            $user2 = User::create([
                'username' => 'oauthuser',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'first_name' => 'OAuth',
                'last_name' => 'User',
                'gender' => 'other',
                'status' => 'active',
                'is_verified' => true,
                'email_verified_at' => now(),
            ]);
        }

        // Create test social accounts
        $socialAccounts = [
            [
                'user_id' => $user1->id,
                'provider' => 'google',
                'provider_id' => '*********',
                'provider_email' => '<EMAIL>',
                'provider_name' => 'Test User',
                'provider_nickname' => 'testuser',
                'provider_avatar' => 'https://lh3.googleusercontent.com/a/default-user',
                'last_used_at' => now()->subDays(1),
            ],
            [
                'user_id' => $user1->id,
                'provider' => 'facebook',
                'provider_id' => '*********',
                'provider_email' => '<EMAIL>',
                'provider_name' => 'Test User FB',
                'provider_nickname' => 'testuser.fb',
                'provider_avatar' => 'https://graph.facebook.com/*********/picture',
                'last_used_at' => now()->subDays(3),
            ],
            [
                'user_id' => $user2->id,
                'provider' => 'google',
                'provider_id' => '*********',
                'provider_email' => '<EMAIL>',
                'provider_name' => 'OAuth User',
                'provider_nickname' => 'oauthuser',
                'provider_avatar' => 'https://lh3.googleusercontent.com/a/oauth-user',
                'last_used_at' => now()->subHours(2),
            ],
        ];

        foreach ($socialAccounts as $accountData) {
            UserSocialAccount::updateOrCreate(
                [
                    'provider' => $accountData['provider'],
                    'provider_id' => $accountData['provider_id'],
                ],
                $accountData
            );
        }

        $this->command->info('Test OAuth social accounts created successfully.');
        $this->command->info('Test users:');
        $this->command->info('- <EMAIL> (linked to Google & Facebook)');
        $this->command->info('- <EMAIL> (linked to Google)');
    }
}

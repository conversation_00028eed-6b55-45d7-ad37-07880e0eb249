<?php

namespace Modules\User\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Role\Models\Permission;
use Modules\Role\Models\Role;

class UserPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedUserPermissions();
        $this->assignPermissionsToSuperAdmin();
    }

    /**
     * Seed user module permissions.
     */
    private function seedUserPermissions(): void
    {
        $permissions = [
            [
                'name' => 'user.view',
                'display_name' => 'View Users',
                'description' => 'Permission to view users list and details',
                'module_name' => 'user',
                'sort_order' => 1,
                'guard_name' => 'api',
            ],
            [
                'name' => 'user.create',
                'display_name' => 'Create Users',
                'description' => 'Permission to create new users',
                'module_name' => 'user',
                'sort_order' => 2,
                'guard_name' => 'api',
            ],
            [
                'name' => 'user.edit',
                'display_name' => 'Edit Users',
                'description' => 'Permission to update existing users and assign roles',
                'module_name' => 'user',
                'sort_order' => 3,
                'guard_name' => 'api',
            ],
            [
                'name' => 'user.delete',
                'display_name' => 'Delete Users',
                'description' => 'Permission to soft delete and restore users',
                'module_name' => 'user',
                'sort_order' => 4,
                'guard_name' => 'api',
            ],
            [
                'name' => 'user.destroy',
                'display_name' => 'Destroy Users',
                'description' => 'Permission to permanently delete users',
                'module_name' => 'user',
                'sort_order' => 5,
                'guard_name' => 'api',
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => $permission['guard_name']],
                $permission
            );
        }
    }

    /**
     * Assign user permissions to super-admin role.
     */
    private function assignPermissionsToSuperAdmin(): void
    {
        $superAdmin = Role::where('name', 'super-admin')->where('guard_name', 'api')->first();
        
        if ($superAdmin) {
            $userPermissions = Permission::where('module_name', 'user')->where('guard_name', 'api')->get();
            
            // Gán tất cả quyền user cho super-admin
            foreach ($userPermissions as $permission) {
                if (!$superAdmin->hasPermissionTo($permission->name)) {
                    $superAdmin->givePermissionTo($permission->name);
                }
            }
        }
    }
}
